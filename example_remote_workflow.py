"""
示例远程工作流客户端

这个脚本演示了如何使用 WorkflowClient SDK 来远程定义和驱动 MeowAgent 的工作流。

运行前请确保：
1. MeowAgent 服务器正在运行（通常在 localhost:8000）
2. 已经在 workflows/ 目录下创建了 my_remote_flow.remote.yaml 标记文件
3. 安装了 websockets 库：pip install websockets

使用方法：
python example_remote_workflow.py
"""

import asyncio
import sys
import os

# 添加当前目录到 Python 路径，以便导入 workflow_client
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from workflow_client import WorkflowClient


async def my_remote_workflow():
    """
    示例远程工作流：新闻搜索和分析
    
    这个工作流演示了如何：
    1. 等待用户输入
    2. 根据用户输入选择不同的处理分支
    3. 执行搜索操作
    4. 生成回复
    """
    
    # 创建客户端并连接到服务器
    client = WorkflowClient()
    
    try:
        print("正在连接到 MeowAgent 服务器...")
        await client.connect("ws://localhost:8000/ws/v1/remote_workflow_connector")
        
        print("正在启动远程工作流实例...")
        session = await client.start_workflow_instance("my_remote_flow")
        
        print("远程工作流已启动，开始执行步骤...")
        
        # 步骤 1: 等待用户输入
        print("步骤 1: 等待用户输入")
        user_message = await session.user_input(
            name="user_input",
            description="等待用户输入消息"
        )
        print(f"收到用户输入: {user_message}")
        
        # 步骤 2: 根据用户输入选择处理分支
        print("步骤 2: 选择处理分支")
        selected_branch = await session.switch(
            name="switch",
            description="根据用户的消息，选择不同的工作流",
            cases=[
                {
                    "name": "search_news", 
                    "description": "搜索新闻"
                },
                {
                    "name": "chat", 
                    "description": "闲聊"
                },
            ]
        )
        print(f"选择的分支: {selected_branch}")
        
        # 根据选择的分支执行不同的逻辑
        if selected_branch == "search_news":
            await handle_search_news(session)
        elif selected_branch == "chat":
            await handle_chat(session)
        else:
            print(f"未知分支: {selected_branch}")
            await session.generate(
                name="unknown_branch_response",
                description="处理未知分支的情况"
            )
        
        # 步骤 N: 结束工作流
        print("结束工作流")
        await session.halt()
        
        print("远程工作流执行完成！")
        
    except Exception as e:
        print(f"远程工作流执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭连接
        await client.close()


async def handle_search_news(session):
    """处理新闻搜索分支"""
    print("执行新闻搜索分支...")
    
    max_attempts = 3
    
    for i in range(max_attempts):
        print(f"搜索尝试 {i+1}/{max_attempts}")
        
        # 执行搜索
        await session.execute(
            name="search_news",
            description="请使用Bing检索新闻",
            actions=[
                {
                    "names": ["playwright"], 
                    "min_calls": 1, 
                    "max_calls": 5
                },
            ],
        )
        
        # 检查搜索结果是否充分
        is_task_complete = await session.condition(
            name="check_news_complete",
            description="评估新闻信息收集完成度，检查是否已经获取到足够的新闻资讯，包括新闻的时效性、相关性和完整性",
        )
        
        print(f"新闻收集完成度检查: {is_task_complete}")
        
        if is_task_complete:
            break
    
    # 生成新闻摘要
    print("生成新闻摘要...")
    await session.generate(
        name="generate_news_summary",
        description="整理并分析收集到的新闻信息，生成结构化的新闻摘要、关键要点和相关分析",
    )


async def handle_chat(session):
    """处理聊天分支"""
    print("执行聊天分支...")
    
    await session.generate(
        name="generate_chat_response",
        description="根据用户的消息，生成回复",
    )


if __name__ == "__main__":
    print("=== MeowAgent 远程工作流示例 ===")
    print("这个示例演示了如何使用 WorkflowClient SDK 远程定义和驱动工作流")
    print()
    
    try:
        asyncio.run(my_remote_workflow())
    except KeyboardInterrupt:
        print("\n用户中断了程序")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
