"""
WorkflowClient SDK - 远程工作流客户端

该模块提供了 WorkflowClient 类，允许外部 Python 脚本通过 WebSocket 连接
远程定义和驱动 MeowAgent 内部的工作流步骤。

WorkflowClient 提供与 WorkflowContext 完全相同的接口，使用户可以像编写
本地工作流一样编写远程工作流。

主要特性：
1. 与 WorkflowContext 完全相同的 API
2. 自动处理 WebSocket 连接和消息协议
3. 支持所有工作流步骤类型（EXECUTE, CONDITION, GENERATE, USER_INPUT, SWITCH, HALT）
4. 异步/await 语法支持
5. 自动错误处理和重连机制

使用示例：
```python
import asyncio
from workflow_client import WorkflowClient

async def my_remote_workflow():
    client = WorkflowClient()
    await client.connect("ws://localhost:8000/ws/v1/remote_workflow_connector")
    
    session = await client.start_workflow_instance("my_remote_flow")
    
    # 等待用户输入
    user_message = await session.user_input("请输入您的问题")
    
    # 执行搜索
    await session.execute(
        description="搜索相关信息",
        actions=[{"names": ["web_search"], "min_calls": 1, "max_calls": 3}]
    )
    
    # 生成回复
    await session.generate("根据搜索结果生成回复")
    
    # 结束工作流
    await session.halt()

if __name__ == "__main__":
    asyncio.run(my_remote_workflow())
```
"""

import asyncio
import json
import uuid
import websockets
import traceback
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

class WorkflowSession:
    """
    工作流会话类，提供与 WorkflowContext 相同的接口。
    
    每个会话对应一个远程工作流实例。
    """
    
    def __init__(self, client: 'WorkflowClient', workflow_instance_id: str, workflow_script_name: str):
        self.client = client
        self.workflow_instance_id = workflow_instance_id
        self.workflow_script_name = workflow_script_name
        self._pending_requests: Dict[str, asyncio.Future] = {}
        
    async def execute(
        self, 
        description: str, 
        actions: List[Dict[str, Any]],
        name: Optional[str] = None
    ) -> None:
        """
        执行一个 EXECUTE 步骤。
        
        参数:
            description: 步骤描述
            actions: 要执行的动作定义列表
            name: 可选的步骤名称
        """
        step_definition = {
            "operation": "EXECUTE",
            "name": name,
            "description": description,
            "actions": actions
        }
        await self._send_step_and_wait(step_definition)
        
    async def condition(
        self,
        description: str,
        name: Optional[str] = None
    ) -> bool:
        """
        执行一个 CONDITION 步骤并返回条件判断结果。
        
        参数:
            description: 条件描述
            name: 可选的步骤名称
            
        返回:
            条件判断的结果 (True/False)
        """
        step_definition = {
            "operation": "CONDITION",
            "name": name,
            "description": description
        }
        result = await self._send_step_and_wait_for_result(step_definition)
        return bool(result)
        
    async def generate(
        self,
        description: str,
        wait_user: bool = False,
        name: Optional[str] = None
    ) -> None:
        """
        执行一个 GENERATE 步骤。
        
        参数:
            description: 生成内容的描述
            wait_user: 是否在生成后等待用户输入
            name: 可选的步骤名称
        """
        step_definition = {
            "operation": "GENERATE",
            "name": name,
            "description": description,
            "wait_user": wait_user
        }
        await self._send_step_and_wait(step_definition)
        
    async def user_input(
        self,
        description: str,
        name: Optional[str] = None
    ) -> Any:  # 返回用户消息对象
        """
        等待用户输入并返回用户消息。
        
        参数:
            description: 等待用户输入的提示描述
            name: 可选的步骤名称
            
        返回:
            用户输入的消息对象
        """
        step_definition = {
            "operation": "USER_INPUT",
            "name": name,
            "description": description
        }
        result = await self._send_step_and_wait_for_result(step_definition)
        return result
        
    async def switch(
        self,
        name: str,
        description: str,
        cases: List[Dict[str, Any]],
        default_handler: Optional[callable] = None
    ) -> str:
        """
        执行一个 SWITCH 步骤，允许根据选择切换到不同的分支。
        
        参数:
            name: 步骤名称
            description: 步骤描述
            cases: 可选择的分支列表
            default_handler: 默认处理函数（远程工作流中暂不支持）
            
        返回:
            被选择的分支名称
        """
        step_definition = {
            "operation": "SWITCH",
            "name": name,
            "description": description,
            "cases": cases
        }
        result = await self._send_step_and_wait_for_result(step_definition)
        return str(result) if result is not None else ""
        
    async def halt(
        self,
        name: Optional[str] = None
    ) -> None:
        """
        终止工作流执行。
        
        参数:
            name: 可选的步骤名称
        """
        step_definition = {
            "operation": "HALT",
            "name": name,
            "description": "工作流终止"
        }
        await self._send_step_and_wait(step_definition)
    
    async def _send_step_and_wait(self, step_definition: Dict[str, Any]) -> None:
        """发送步骤定义并等待完成"""
        await self._send_step_and_wait_for_result(step_definition)
    
    async def _send_step_and_wait_for_result(self, step_definition: Dict[str, Any]) -> Any:
        """发送步骤定义并等待结果"""
        client_request_id = f"req_{uuid.uuid4().hex[:8]}"
        
        # 创建等待结果的 Future
        result_future = asyncio.Future()
        self._pending_requests[client_request_id] = result_future
        
        # 发送步骤定义
        message = {
            "type": "define_step",
            "workflow_instance_id": self.workflow_instance_id,
            "client_request_id": client_request_id,
            "step_definition": step_definition
        }
        
        await self.client._send_message(message)
        
        try:
            # 等待结果
            result = await result_future
            return result
        finally:
            # 清理
            if client_request_id in self._pending_requests:
                del self._pending_requests[client_request_id]
    
    def _handle_step_result(self, client_request_id: str, result: Any):
        """处理步骤结果"""
        if client_request_id in self._pending_requests:
            future = self._pending_requests[client_request_id]
            if not future.done():
                future.set_result(result)


class WorkflowClient:
    """
    远程工作流客户端，提供与 MeowAgent 服务器的连接和通信功能。
    """
    
    def __init__(self):
        self.websocket = None
        self.url = None
        self._sessions: Dict[str, WorkflowSession] = {}
        self._message_handler_task = None
        self._start_instance_future = None
        
    async def connect(self, url: str):
        """
        连接到 MeowAgent 服务器。
        
        参数:
            url: WebSocket 服务器地址，例如 "ws://localhost:8000/ws/v1/remote_workflow_connector"
        """
        self.url = url
        try:
            self.websocket = await websockets.connect(url)
            print(f"已连接到 MeowAgent 服务器: {url}")
            
            # 启动消息处理任务
            self._message_handler_task = asyncio.create_task(self._handle_messages())
            
        except Exception as e:
            print(f"连接失败: {e}")
            raise
    
    async def start_workflow_instance(self, workflow_script_name: str) -> WorkflowSession:
        """
        启动一个新的工作流实例。

        参数:
            workflow_script_name: 远程工作流脚本名称

        返回:
            WorkflowSession 对象
        """
        if not self.websocket:
            raise RuntimeError("尚未连接到服务器，请先调用 connect()")

        # 创建等待响应的 Future
        response_future = asyncio.Future()
        self._start_instance_future = response_future

        message = {
            "type": "start_workflow_instance",
            "workflow_script_name": workflow_script_name
        }

        await self._send_message(message)

        # 等待服务器响应
        try:
            response_data = await asyncio.wait_for(response_future, timeout=10.0)
            workflow_instance_id = response_data.get("workflow_instance_id")

            if not workflow_instance_id:
                raise RuntimeError("服务器响应中缺少 workflow_instance_id")

            session = WorkflowSession(self, workflow_instance_id, workflow_script_name)
            self._sessions[workflow_instance_id] = session

            return session

        except asyncio.TimeoutError:
            raise RuntimeError("等待服务器响应超时")
        finally:
            self._start_instance_future = None
    
    async def _send_message(self, message: Dict[str, Any]):
        """发送消息到服务器"""
        if not self.websocket:
            raise RuntimeError("WebSocket 连接不可用")
        
        message_json = json.dumps(message)
        await self.websocket.send(message_json)
        print(f"发送消息: {message_json}")
    
    async def _handle_messages(self):
        """处理来自服务器的消息"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self._process_message(data)
                except Exception as e:
                    print(f"处理消息时出错: {e}")
                    print(f"原始消息: {message}")
        except Exception as e:
            print(f"消息处理循环出错: {e}")
    
    async def _process_message(self, data: Dict[str, Any]):
        """处理单个消息"""
        message_type = data.get("type")
        
        if message_type == "workflow_instance_started":
            # 处理工作流实例启动响应
            workflow_instance_id = data.get("workflow_instance_id")
            print(f"工作流实例已启动: {workflow_instance_id}")

            # 如果有等待启动响应的 Future，设置结果
            if self._start_instance_future and not self._start_instance_future.done():
                self._start_instance_future.set_result(data)
            
        elif message_type == "step_result":
            # 处理步骤结果
            workflow_instance_id = data.get("workflow_instance_id")
            client_request_id = data.get("client_request_id")
            result = data.get("result")
            
            session = self._sessions.get(workflow_instance_id)
            if session:
                session._handle_step_result(client_request_id, result)
            
        elif message_type == "request_next_step_definition":
            # 服务器请求下一步骤定义
            # 在这个简化版本中，我们不需要处理这个消息
            # 因为步骤定义是由用户代码主动发送的
            pass
            
        elif message_type == "error":
            error_message = data.get("message", "未知错误")
            print(f"服务器错误: {error_message}")
            
        else:
            print(f"未知消息类型: {message_type}")
    
    async def close(self):
        """关闭连接"""
        if self._message_handler_task:
            self._message_handler_task.cancel()
            try:
                await self._message_handler_task
            except asyncio.CancelledError:
                pass
        
        if self.websocket:
            await self.websocket.close()
            print("连接已关闭")
