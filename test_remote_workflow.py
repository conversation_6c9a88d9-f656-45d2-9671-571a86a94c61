"""
远程工作流功能测试脚本

这个脚本用于测试远程工作流的基本功能，包括：
1. 连接到服务器
2. 启动工作流实例
3. 发送简单的步骤定义
4. 接收结果

运行前请确保 MeowAgent 服务器正在运行。
"""

import asyncio
import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from workflow_client import WorkflowClient


async def test_basic_connection():
    """测试基本连接功能"""
    print("=== 测试基本连接功能 ===")
    
    client = WorkflowClient()
    
    try:
        print("1. 连接到服务器...")
        await client.connect("ws://localhost:8000/ws/v1/remote_workflow_connector")
        print("✓ 连接成功")
        
        print("2. 启动工作流实例...")
        session = await client.start_workflow_instance("my_remote_flow")
        print("✓ 工作流实例启动成功")
        
        print("3. 发送简单的生成步骤...")
        await session.generate(
            name="test_generate",
            description="这是一个测试生成步骤"
        )
        print("✓ 生成步骤执行成功")
        
        print("4. 结束工作流...")
        await session.halt()
        print("✓ 工作流结束成功")
        
        print("✓ 所有测试通过！")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await client.close()
        print("连接已关闭")
    
    return True


async def test_complex_workflow():
    """测试复杂工作流功能"""
    print("\n=== 测试复杂工作流功能 ===")
    
    client = WorkflowClient()
    
    try:
        print("1. 连接到服务器...")
        await client.connect("ws://localhost:8000/ws/v1/remote_workflow_connector")
        
        print("2. 启动工作流实例...")
        session = await client.start_workflow_instance("my_remote_flow")
        
        print("3. 测试条件步骤...")
        result = await session.condition(
            name="test_condition",
            description="这是一个测试条件：天气是否晴朗？"
        )
        print(f"✓ 条件步骤结果: {result}")
        
        print("4. 测试执行步骤...")
        await session.execute(
            name="test_execute",
            description="执行测试操作",
            actions=[
                {
                    "names": ["test_action"],
                    "min_calls": 1,
                    "max_calls": 1
                }
            ]
        )
        print("✓ 执行步骤完成")
        
        print("5. 测试分支选择...")
        selected = await session.switch(
            name="test_switch",
            description="选择一个测试分支",
            cases=[
                {"name": "branch_a", "description": "分支 A"},
                {"name": "branch_b", "description": "分支 B"}
            ]
        )
        print(f"✓ 选择的分支: {selected}")
        
        print("6. 结束工作流...")
        await session.halt()
        
        print("✓ 复杂工作流测试通过！")
        
    except Exception as e:
        print(f"✗ 复杂工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await client.close()
    
    return True


async def main():
    """主测试函数"""
    print("MeowAgent 远程工作流功能测试")
    print("=" * 50)
    
    # 测试基本连接
    basic_success = await test_basic_connection()
    
    if basic_success:
        # 如果基本测试通过，进行复杂测试
        complex_success = await test_complex_workflow()
        
        if complex_success:
            print("\n🎉 所有测试都通过了！远程工作流功能正常工作。")
        else:
            print("\n⚠️ 基本功能正常，但复杂功能测试失败。")
    else:
        print("\n❌ 基本功能测试失败，请检查服务器连接。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试程序出错: {e}")
        import traceback
        traceback.print_exc()
