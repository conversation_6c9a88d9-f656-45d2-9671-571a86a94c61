"""
安装远程工作流功能所需的依赖

这个脚本会检查并安装远程工作流功能所需的 Python 包。
"""

import subprocess
import sys
import importlib


def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False


def install_package(package_name):
    """安装包"""
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package_name} 安装失败: {e}")
        return False


def main():
    """主函数"""
    print("MeowAgent 远程工作流依赖检查和安装")
    print("=" * 50)
    
    # 需要的包列表
    required_packages = [
        "websockets",  # WebSocket 客户端支持
        "pydantic",    # 数据模型（通常已安装）
        "fastapi",     # Web 框架（通常已安装）
        "asyncio",     # 异步支持（Python 内置）
    ]
    
    # 检查每个包
    missing_packages = []
    for package in required_packages:
        if package == "asyncio":
            # asyncio 是 Python 3.4+ 的内置模块
            continue
            
        print(f"检查 {package}...", end=" ")
        if check_package(package):
            print("✓ 已安装")
        else:
            print("✗ 未安装")
            missing_packages.append(package)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n需要安装 {len(missing_packages)} 个包:")
        for package in missing_packages:
            print(f"  - {package}")
        
        print("\n开始安装...")
        failed_packages = []
        for package in missing_packages:
            if not install_package(package):
                failed_packages.append(package)
        
        if failed_packages:
            print(f"\n❌ 以下包安装失败:")
            for package in failed_packages:
                print(f"  - {package}")
            print("\n请手动安装这些包:")
            for package in failed_packages:
                print(f"  pip install {package}")
        else:
            print("\n🎉 所有依赖都已成功安装！")
    else:
        print("\n✓ 所有依赖都已安装，无需额外操作。")
    
    print("\n远程工作流功能现在可以使用了！")
    print("请参考 REMOTE_WORKFLOW_README.md 了解使用方法。")


if __name__ == "__main__":
    main()
