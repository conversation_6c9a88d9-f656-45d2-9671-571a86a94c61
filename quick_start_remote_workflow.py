"""
远程工作流快速启动脚本

这个脚本提供了一个简单的方式来快速测试远程工作流功能。
它会启动一个最小化的远程工作流示例。
"""

import asyncio
import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from workflow_client import WorkflowClient


async def quick_start_demo():
    """快速启动演示"""
    print("🚀 MeowAgent 远程工作流快速启动演示")
    print("=" * 50)
    
    # 检查服务器连接
    print("1. 检查服务器连接...")
    client = WorkflowClient()
    
    try:
        await client.connect("ws://localhost:8000/ws/v1/remote_workflow_connector")
        print("✓ 成功连接到 MeowAgent 服务器")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("\n请确保:")
        print("  1. MeowAgent 服务器正在运行 (python api.py)")
        print("  2. 服务器运行在 localhost:8000")
        return False
    
    try:
        # 启动工作流
        print("\n2. 启动远程工作流实例...")
        session = await client.start_workflow_instance("my_remote_flow")
        print("✓ 工作流实例启动成功")
        
        # 简单的工作流步骤
        print("\n3. 执行工作流步骤...")
        
        # 步骤 1: 生成欢迎消息
        print("   步骤 1: 生成欢迎消息")
        await session.generate(
            name="welcome",
            description="生成一个友好的欢迎消息，介绍远程工作流功能"
        )
        print("   ✓ 欢迎消息生成完成")
        
        # 步骤 2: 条件判断
        print("   步骤 2: 条件判断")
        is_ready = await session.condition(
            name="readiness_check",
            description="检查用户是否准备好继续使用远程工作流功能"
        )
        print(f"   ✓ 条件判断结果: {is_ready}")
        
        # 步骤 3: 根据条件执行不同操作
        if is_ready:
            print("   步骤 3a: 用户已准备好，提供详细指导")
            await session.generate(
                name="detailed_guide",
                description="提供详细的远程工作流使用指导和最佳实践"
            )
        else:
            print("   步骤 3b: 用户未准备好，提供基础信息")
            await session.generate(
                name="basic_info",
                description="提供远程工作流的基础信息和简单介绍"
            )
        
        # 步骤 4: 结束工作流
        print("   步骤 4: 结束工作流")
        await session.halt()
        print("   ✓ 工作流正常结束")
        
        print("\n🎉 快速启动演示完成！")
        print("\n远程工作流功能正常工作。您现在可以:")
        print("  1. 查看 example_remote_workflow.py 了解更复杂的示例")
        print("  2. 阅读 REMOTE_WORKFLOW_README.md 了解详细文档")
        print("  3. 编写自己的远程工作流脚本")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流执行出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await client.close()
        print("\n连接已关闭")


async def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    issues = []

    # 检查 websockets 库
    try:
        import websockets
        print("✓ websockets 库已安装")
    except ImportError:
        issues.append("缺少 websockets 库，请运行: pip install websockets")
    
    if issues:
        print("\n❌ 发现以下问题:")
        for issue in issues:
            print(f"  - {issue}")
        print("\n请解决这些问题后重新运行。")
        return False
    
    print("✓ 所有前置条件都满足")
    return True


async def main():
    """主函数"""
    print("MeowAgent 远程工作流快速启动")
    print("=" * 50)
    
    # 检查前置条件
    if not await check_prerequisites():
        return
    
    print()
    
    # 运行演示
    success = await quick_start_demo()
    
    if success:
        print("\n✅ 快速启动成功完成！")
    else:
        print("\n❌ 快速启动遇到问题，请检查日志。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n用户中断了程序")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()
