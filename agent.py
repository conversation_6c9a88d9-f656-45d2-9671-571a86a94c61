"""
该模块定义了智能体 (`Agent`) 的核心实现，是整个智能体系统的驱动中心。
主要功能和逻辑包括：

1.  **Agent 类**:
    *   **初始化与配置**:
        *   接收并管理核心组件，如语言模型 (`OpenAIModel`)、对话状态 (`Conversation`)、
          输出连接 (`Connection`，用于与客户端通信)。
        *   根据配置加载和初始化各类工具库 (`Library`)，包括本地库和通过
          `MCPClientLibrary` 连接的远程工具。
        *   支持Agent级别的特定配置，如默认工作流。

    *   **对话流程管理**:
        *   通过 `handle_incoming_message` 方法处理来自客户端的各类消息，主要是用户输入
          (`user_input`) 和特定事件（如 `user_ready`）。
        *   核心处理逻辑在 `_process_user_input` 中，负责协调整个响应生成过程。
        *   与 `Conversation` 对象深度集成，记录和管理完整的对话历史，
          包括用户消息、助手回复、系统消息以及工具调用和结果。

    *   **与大型语言模型 (LLM) 交互**:
        *   `_prepare_messages_for_model`: 动态构建发送给LLM的上下文，整合了
          基础系统提示、来自各个库的动态提示信息、以及可能的少样本示例 (few-shot examples)。
        *   `_call_model`: 调用LLM进行推理，支持流式 (`stream_mode`) 和非流式响应。
          在流式模式下，通过 `handle_stream_chunk` 回调函数逐步发送模型的思考过程、
          内容增量以及工具调用信息。

    *   **工具调用与执行**:
        *   `_collect_available_tools`: 收集所有已注册库提供的工具，并根据当前
          工作流 (Workflow) 状态过滤允许使用的工具。
        *   当LLM决定使用工具时，`Agent` 会解析工具调用请求。
        *   `_process_multiple_tool_calls` 和 `_execute_single_tool`: 负责并发或
          单个执行工具。调用工具库中对应的 `execute_tool` 方法。
        *   工具执行结果会作为 `ToolMessage` 添加到对话历史中，供LLM进一步处理。

    *   **工作流 (Workflow) 集成**:
        *   与 `WorkflowLibrary` (一种特殊的 `Library`) 协同工作，允许定义和执行
          复杂的、多步骤的任务流程。
        *   工作流可以控制对话流程，例如决定何时等待用户输入 (`UserInputStep`)、
          何时调用模型、以及哪些工具在特定步骤可用。
        *   库的 `pre_process` 和 `post_process` 方法（特别是工作流库）可以在
          模型调用前后对消息和状态进行处理和验证，甚至触发重新生成。

    *   **状态更新与输出**:
        *   通过 `_send_message` 方法将智能体的各类活动信息（如消息对象、状态更新、
          错误信息、工具调用详情等）发送到 `output_connection`。
        *   内容截断 (`_truncate_string`) 确保发送给客户端的数据不会过长。

    *   **异步架构**:
        *   整个Agent及其大部分操作都是异步的 (`async/await`)，以实现高效的并发处理，
          这对于处理网络请求、模型调用和多用户场景至关重要。

    *   **生命周期与资源管理**:
        *   `_initialize_libraries`: 异步初始化已注册的库。
        *   `close`: 关闭并清理Agent持有的特定资源，如部分库的连接。
        *   `clone_for_new_conversation`: 支持基于现有Agent模板快速创建新的Agent实例，
          以服务新对话，并能复用共享资源（如已初始化的MCP库和模型实例）。

2.  **辅助功能**:
    *   `load_library_classes_from_directory`: 一个工具函数，用于在启动时从指定的
      `libraries` 目录动态扫描并加载所有继承自 `Library` 基类的Python类。

该模块旨在提供一个灵活、可扩展的智能体框架，能够通过组合不同的工具库和工作流来
适应多样化的应用场景。
"""

import os
import sys
import json
import asyncio
import traceback
import importlib
import inspect
from conversation import Conversation
from log import logger
from typing import List, Dict, Any, Optional, Union, Awaitable, Callable, Type
from message import Message, UserMessage, AssistantMessage, SystemMessage, ToolMessage, ToolCallFunction, ToolCall, AgentSystemMessage
from utils import dumps
from datetime import datetime

# 导入基础库类
from libraries.library import Library
from libraries.workflow import WorkflowLibrary
from libraries.workflow_models import UserInputStep
from model import OpenAIModel
import config
from connection import Connection
from libraries.mcp_client import MCPClientLibrary


def load_library_classes_from_directory(directory='libraries'):
    """
    从指定目录加载所有继承自Library的类
    
    参数:
        directory: 库文件所在的目录路径
        
    返回:
        所有Library子类的类引用字典，键为类名
    """
    library_classes = {}
    
    # 确保目录存在
    if not os.path.exists(directory) or not os.path.isdir(directory):
        logger.error(f"无法加载库: 目录 '{directory}' 不存在或不是一个目录")
        return library_classes
    
    # 遍历目录中的所有Python文件
    for filename in os.listdir(directory):
        # 只处理Python文件，且不处理__init__.py
        if filename.endswith('.py') and filename != '__init__.py' and filename != 'library.py':
            module_name = filename[:-3]  # 去掉.py后缀
            full_module_name = f"libraries.{module_name}"
            
            try:
                # 尝试导入模块
                module = importlib.import_module(full_module_name)
                
                # 遍历模块中的所有对象
                for name, obj in inspect.getmembers(module):
                    # 检查是否是类，是否是Library的子类，但不是Library本身
                    if (inspect.isclass(obj) and 
                        issubclass(obj, Library) and 
                        obj != Library):
                        library_classes[name] = obj
                        logger.info(f"成功加载库类: {name} 从模块 {full_module_name}")
            
            except Exception as e:
                logger.error(f"加载模块 {full_module_name} 时出错: {traceback.format_exc()}")
    
    return library_classes


class Agent:
    """
    智能体类，负责对话管理、工具调用和状态保存/恢复
    """
    def __init__(self, 
                model: OpenAIModel, # Type hint
                conversation: Conversation, 
                output_connection: Optional[Connection] = None,
                available_library_classes: Optional[Dict[str, Type[Library]]] = None,
                library_config: Optional[Dict[str, Any]] = None,
                initialized_mcp_libraries: Optional[Dict[str, Library]] = None,
                agent_config: Optional[Dict[str, Any]] = None):
        # 初始化核心组件
        self.model: OpenAIModel = model
        self.conversation: Conversation = conversation # Type hint
        self.conversation_id: Optional[str] = conversation.conversation_id  # 记录对话ID
        self.libraries: Dict[str, Library] = {}  # 工具库注册表, type hint
        self.output_connection: Optional[Connection] = output_connection  # 消息连接，用于UI通信
        
        # 保存预加载的资源
        self.available_library_classes: Dict[str, Type[Library]] = available_library_classes or {}
        self.library_config: Dict[str, Any] = library_config or {"libraries": []}
        self.initialized_mcp_libraries: Dict[str, Library] = initialized_mcp_libraries or {}
        self.agent_config: Dict[str, Any] = agent_config or {}

        # 注册内置工具库
        self.register_built_in_libraries() # This is sync
        
        # 启动库初始化任务 (如果库有异步初始化需求)
        asyncio.create_task(self._initialize_libraries()) # This is async

        # conversation.load_history() 现在是异步的, 并且在 Conversation 内部不再有 load_history
        # History 的加载现在是隐式的，当调用 get_messages 等方法时
        # 如果需要在 Agent 初始化时强制加载，可以这样做：
        # asyncio.create_task(self.conversation.get_messages()) 
        # 但通常不需要，因为首次需要消息时会自动加载


    def _truncate_string(self, text: str, max_length: int, suffix: str) -> str:
        """
        根据最大字符长度截断字符串，并在需要时添加后缀。

        参数:
            text: 需要截断的原始字符串。
            max_length: 最大允许的字符长度。
            suffix: 截断时要添加的后缀字符串。

        返回:
            截断后的字符串，如果原始长度未超过限制则返回原字符串。
        """
        if len(text) > max_length:
            truncated_length = max_length - len(suffix)
            return text[:truncated_length] + suffix
        return text

    async def _initialize_libraries(self):
        """初始化所有已注册的Agent特定库（共享库已在AgentManager中初始化）"""
        for library_name, library in self.libraries.items():
            if library_name in self.initialized_mcp_libraries:
                logger.debug(f"跳过已初始化的共享库: {library_name}")
                continue
                
            try:
                if hasattr(library, 'initialize'):
                    initialize_method = getattr(library, 'initialize')
                    if callable(initialize_method) and inspect.iscoroutinefunction(initialize_method):
                        logger.info(f"正在初始化库: {library_name}")
                        await initialize_method()
                    elif callable(initialize_method):
                        logger.info(f"正在初始化库(同步): {library_name}")
                        initialize_method()
            except Exception as e:
                logger.error(f"初始化库 {library_name} 失败: {traceback.format_exc()}")
                await self._send_message("error", f"初始化库 {library_name} 失败: {str(e)}")

    async def _send_message(self, message_type: str, content: Any):
        """
        发送消息到队列

        参数:
            message_type: 消息类型
            content: 消息内容
        """
        if self.output_connection:
            # 确保 content 是字符串，如果它是其他类型，尝试转换为字符串
            display_content = content
            if not isinstance(content, str):
                try:
                    display_content = dumps(content) # 使用 dumps 处理复杂对象
                except Exception:
                    display_content = str(content) # 回退到 str()
            
            truncated_content = self._truncate_string(display_content, config.CLIENT_DISPLAY_MAX_LENGTH, config.TRUNCATION_SUFFIX)

            message = {
                "type": message_type,
                "content": truncated_content, # 发送截断后的内容
                "conversation_id": self.conversation_id,
                "timestamp": dumps({"time": asyncio.get_event_loop().time()})
            }
            
            workflow_lib = None
            for lib in self.libraries.values():
                if isinstance(lib, WorkflowLibrary):
                    workflow_lib = lib
                    break
                    
            if workflow_lib and workflow_lib.state.workflow_name:
                current_step = workflow_lib.state.get_current_step()
                if current_step:
                    step_display_name = ""
                    if workflow_lib.state.source_type == 'yaml' and workflow_lib.state.workflow_data:
                        step_display_name = workflow_lib.state.workflow_data.get_step_display_name(current_step)
                    else:
                        step_display_name = current_step.name if hasattr(current_step, 'name') and current_step.name else "未命名步骤"
                    
                    message["workflow_info"] = {
                        "workflow_name": workflow_lib.state.workflow_name,
                        "step_index": workflow_lib.state.step_index,
                        "step_name": current_step.name if hasattr(current_step, 'name') and current_step.name else step_display_name,
                        "step_operation": current_step.operation if hasattr(current_step, 'operation') else None
                    }
                else:
                     logger.warning(f"WorkflowLibrary {workflow_lib.name} 的当前步骤为空，无法获取步骤信息")
            
            await self.output_connection.send(message)

    def register_built_in_libraries(self):
        """
        注册内置工具库。
        """
        logger.info("开始注册内置工具库...")
        available_library_classes = self.available_library_classes
        enabled_library_names = self.library_config.get("libraries", [])
        
        logger.info(f"使用预加载库类 ({len(available_library_classes)} 个) 和配置的库 ({len(enabled_library_names)} 个)")

        for lib_name, lib_instance in self.initialized_mcp_libraries.items():
            self.register_library(lib_instance)
            logger.info(f"已注册共享MCP库: {lib_name}")

        for class_name in enabled_library_names:
            if class_name in self.initialized_mcp_libraries:
                continue
                
            if class_name in available_library_classes:
                lib_class = available_library_classes[class_name]
                try:
                    default_workflow_name_to_use = self.agent_config.get(
                        'default_workflow_name', config.DEFAULT_WORKFLOW_NAME
                    )
                    logger.debug(f"为 Agent {self.conversation_id} 使用默认工作流: {default_workflow_name_to_use}")
                    
                    library_specific_config = {
                        "agent": self, 
                        "conversation": self.conversation,
                        "default_workflow_name": default_workflow_name_to_use,
                    }
                    
                    library_instance = lib_class(config=library_specific_config)
                    if library_instance:
                        self.register_library(library_instance)
                        logger.info(f"根据 library.json 成功注册 Agent 特定库: {class_name}")
                except Exception as e:
                    logger.error(f"实例化或注册库 {class_name} 时出错: {traceback.format_exc()}")
            else:
                logger.warning(f"在 library.json 中指定的库 '{class_name}' 未找到或无法加载。")
        logger.info("内置工具库注册完成。")

    def register_library(self, library: Library):
        """
        注册一个工具库到智能体

        参数:
            library: 工具库实例
        """
        if library.name in self.libraries:
            logger.warning(f"库 '{library.name}' 已注册，将被覆盖。")
        self.libraries[library.name] = library
        
        # 调用库的 set_agent 方法建立库与 Agent 的引用关系
        if hasattr(library, 'set_agent'):
            library.set_agent(self)
            logger.debug(f"已为库 '{library.name}' 设置 Agent 引用")

    async def _prepare_messages_for_model(self):
        """
        准备发送给模型的消息对象列表，包括结构化系统消息和历史消息。
        主要通过创建 AgentSystemMessage 并将其添加到对话中（如果尚未存在或需要更新）。
        实际的消息列表编译（包括 AgentSystemMessage 展开）由 Conversation.generate 内部的渲染管道处理。
        """
        base_system_message_content = config.SYSTEM_MESSAGE
        library_prompts = []
        few_shot_providers = []
        for library in self.libraries.values():
            prompt = library.get_prompt()
            if prompt:
                library_prompts.append(prompt)
            if hasattr(library, "get_few_shot_examples") and callable(getattr(library, "get_few_shot_examples")):
                few_shot_providers.append(library)

        few_shot_examples_groups: List[List[Message]] = [] # 类型提示
        for provider in few_shot_providers:
            dict_examples_list = provider.get_few_shot_examples() # 这是 List[List[Dict]]
            for example_group_dicts in dict_examples_list:
                message_group: List[Message] = []
                for msg_dict in example_group_dicts:
                    try:
                        # 使用 Message.from_dict 进行反序列化
                        message_group.append(Message.from_dict(msg_dict))
                    except Exception as e_fs_deserialize:
                        logger.error(f"反序列化 few-shot 示例消息时出错: {msg_dict}, 错误: {e_fs_deserialize}")
                if message_group:
                    few_shot_examples_groups.append(message_group)

        agent_system_message = AgentSystemMessage(
            base_prompt=base_system_message_content,
            library_prompts=library_prompts,
            few_shot_examples=few_shot_examples_groups
        )
        
        # 现在 conversation.add_message 是异步的
        # AgentSystemMessage 通常只在对话开始时或配置更改时添加/更新
        # 避免每次都添加，可以检查历史中是否已存在相似的 AgentSystemMessage
        # 或者，更简单的是，依赖渲染管道的 ReorderRenderer 来处理多个 AgentSystemMessage
        # ReorderRenderer 会保留最后一个，并展开它。所以重复添加（只要内容相同）是安全的。
        await self.conversation.add_message(agent_system_message)


    def _collect_available_tools(self) -> Optional[List[Dict[str, Any]]]: # Return type can be None
        """
        收集所有可用工具，并根据当前工作流步骤进行过滤

        返回:
            OpenAI格式的工具列表，如果无工具则为 None
        """
        all_tools_definitions: List[Dict[str, Any]] = [] # Type hint
        for library in self.libraries.values():
            # library.get_tools() 返回 List[Dict], 每个 dict 是工具的 JSON schema
            all_tools_definitions.extend(library.get_tools())

        if not all_tools_definitions:
            return None

        workflow_lib = None
        for lib in self.libraries.values():
            if isinstance(lib, WorkflowLibrary):
                workflow_lib = lib
                break

        final_tool_definitions = all_tools_definitions
        if workflow_lib:
            allowed_tools_names = workflow_lib.get_allowed_tools(agent=self)
            if allowed_tools_names is not None: # None 表示允许所有
                filtered_definitions = []
                for tool_def in all_tools_definitions:
                    # tool_def 是一个描述工具的字典，其中包含 'name' 键
                    # 但 OpenAI 的工具格式是 {"type": "function", "function": {"name": ..., ...}}
                    # 我们需要从原始的库工具定义中获取名称进行比较
                    # 假设 library.get_tools() 返回的每个字典直接包含 "name"
                    if tool_def.get('name') in allowed_tools_names:
                        filtered_definitions.append(tool_def)
                final_tool_definitions = filtered_definitions
                logger.debug(f"根据工作流步骤过滤后可用工具定义: {[t.get('name') for t in final_tool_definitions]}")
            else:
                 logger.debug("工作流库未返回特定允许工具列表，使用所有工具定义。")
        else:
             logger.debug("未找到工作流库，使用所有工具定义。")

        # Conversation.convert_tools 将库的工具定义转换为OpenAI格式
        return self.conversation.convert_tools(final_tool_definitions)

    async def _call_model(self, available_tools: Optional[List[Dict[str, Any]]], stream_mode=False) -> Message:
        """
        调用模型生成回复

        参数:
            available_tools: OpenAI格式的可用工具列表 (Optional)
            stream_mode: 是否使用流式模式 (主要影响UI回调的发送)

        返回:
            模型回复 (Message object)
        """
        try:
            has_seen_content = False
            has_seen_reasoning = False

            async def handle_stream_chunk(chunk: Dict[str, Any]): # Type hint
                nonlocal has_seen_content, has_seen_reasoning
                chunk_type = chunk.get("type", "")
                
                if chunk_type == "content_delta":
                    content = chunk.get("content", "")
                    if content:
                        if not has_seen_content and not has_seen_reasoning: # Only send start if no reasoning started yet
                            await self._send_message("assistant_delta_start", {}) 
                        has_seen_content = True
                        await self._send_message("assistant_delta", content)
                
                elif chunk_type == "reasoning_delta":
                    content = chunk.get("content", "")
                    if content:
                        if not has_seen_reasoning and not has_seen_content: # Only send start if no content started yet
                            await self._send_message("assistant_delta_start", {})
                        has_seen_reasoning = True
                        await self._send_message("reasoning_delta", content)
                
                elif chunk_type == "tool_call_start":
                    await self._send_message("tool_call_start", {"id": chunk.get("id")})
                
                elif chunk_type == "tool_call_delta":
                    await self._send_message("tool_call_delta", {
                        "id": chunk.get("id"),
                        "name": chunk.get("name"),
                        "arguments": chunk.get("arguments")
                    })
                
                elif chunk_type == "done":
                    if has_seen_content or has_seen_reasoning: # Only send done if something was streamed
                        await self._send_message("assistant_delta_done", {})
            
            # conversation.generate 现在是异步的
            return await self.conversation.generate(
                model=self.model,
                tools=available_tools,
                stream_callback=handle_stream_chunk if stream_mode else None # Pass callback only if stream_mode
            )

        except Exception:
            error_msg = f"调用模型出错: {traceback.format_exc()}"
            await self._send_message("error", error_msg)
            # 返回一个错误消息对象或重新抛出以指示严重故障
            return SystemMessage(f"模型调用失败: {error_msg}") # Or raise specific error

    def _serialize_message_for_payload(self, message: Message) -> Dict[str, Any]:
        """
        将单个 Message 对象序列化为适合 WebSocket payload 的字典。
        """
        # 直接使用 message.to_dict()，因为它已经按新规范实现
        return message.to_dict()

    async def handle_incoming_message(self, message_data: Dict[str, Any]): # Renamed for clarity
        """
        处理接收到的消息

        参数:
            message_data: 消息字典，包含type和content字段

        返回:
            处理结果 (通常是None，或特定命令的响应)
        """
        message_type = message_data.get("type", "")
        content = message_data.get("content", "")
        stream_mode = message_data.get("stream_mode", False)

        if message_type == "user_input":
            return await self._process_user_input(content, stream_mode)
        elif message_type == "user_ready":
            # 处理用户就绪消息，启动工作流但不添加用户消息
            logger.info(f"对话 {message_data.get('conversation_id', 'unknown')} 用户就绪")
            return await self._process_user_input(input_text=None, stream_mode=stream_mode)
        elif message_type == "user_disconnect":
            logger.info(f"对话 {message_data.get('conversation_id', 'unknown')} 断开连接: {content}")
            # No specific return needed, agent will be closed elsewhere if necessary
            return {"success": True, "message": "User disconnected event handled"}
        elif message_type == "system_command":
            await self._send_message("info", f"收到系统命令: {content}，但尚未实现处理逻辑")
            return {"success": False, "error": "系统命令尚未实现"}
        else:
            await self._send_message("warning", f"收到未知类型的消息: {message_type}")
            return {"success": False, "error": f"未知消息类型: {message_type}"}

    async def _process_user_input(self, input_text: Optional[str] = None, stream_mode=False) -> Optional[str]: # Return can be None
        """
        处理用户输入，生成回复

        参数:
            input_text: 用户输入文本，如果为None则表示用户就绪但无实际输入
            stream_mode: 是否启用流式模式

        返回:
            助手回复的最终文本内容 (如果适用，否则为None)
        """
        # 创建用户消息（如果有输入）或者一个空的消息对象（用于user_ready）
        user_message = None
        if input_text is not None:
            user_message = UserMessage(input_text)
            await self.conversation.add_message(user_message) # Async
            await self._send_message("full_message_object", self._serialize_message_for_payload(user_message))

        # 获取工作流库实例（如果存在）
        workflow_lib = None
        for library in self.libraries.values():
            if isinstance(library, WorkflowLibrary):
                workflow_lib = library
                break

        # 调用库的 pre_process 方法（包括工作流库）
        for library in self.libraries.values():
            if hasattr(library, "pre_process") and callable(getattr(library, "pre_process")):
                # conversation.get_formatted_messages 是异步的
                formatted_messages = await self.conversation.get_formatted_messages(exclude_system=True)
                # pre_process 本身可能是异步的
                pre_process_method = getattr(library, "pre_process")
                if asyncio.iscoroutinefunction(pre_process_method):
                    await pre_process_method(user_message, formatted_messages)
                else:
                    pre_process_method(user_message, formatted_messages)

        # 检查工作流状态，决定是否需要生成回复
        skip_model_generation = False
        if workflow_lib and workflow_lib.state.workflow_name:
            # 检查当前步骤
            current_step = workflow_lib.state.get_current_step()
            
            # 如果当前是UserInputStep或工作流正在等待用户输入，不需要生成回复
            if isinstance(current_step, UserInputStep) or workflow_lib.state.is_waiting_for_user:
                logger.info(f"工作流 '{workflow_lib.state.workflow_name}' 现在正在等待用户输入，跳过模型生成")
                skip_model_generation = True
            else:
                # 其他步骤类型，生成回复
                logger.info(f"工作流 '{workflow_lib.state.workflow_name}' 当前步骤类型为 {type(current_step).__name__}，继续模型生成")
                skip_model_generation = False

        try:
            final_assistant_content_parts = [] # Store parts of assistant text response
            
            # 如果工作流指示跳过模型生成，则直接返回
            if skip_model_generation:
                return None

            while True:
                await self._prepare_messages_for_model() # Async
                
                # conversation.count_tokens 是异步的
                trimmed_token_count = await self.conversation.count_tokens(self.model)
                if trimmed_token_count > 0:
                    await self._send_message("info", f"准备发送给模型的token数: {trimmed_token_count}")
                
                available_tools = self._collect_available_tools() # Sync
                assistant_response_obj = await self._call_model(available_tools, stream_mode) # Async

                needs_regeneration = False
                
                for library in self.libraries.values():
                    if hasattr(library, "post_process") and callable(getattr(library, "post_process")):
                        # conversation.get_messages 是异步的
                        current_history_messages = await self.conversation.get_messages()
                        post_process_method = getattr(library, "post_process")
                        
                        # post_process 可能是同步或异步
                        if asyncio.iscoroutinefunction(post_process_method):
                            lib_regen, additional_msgs = await post_process_method(
                                assistant_response_obj, current_history_messages
                            )
                        else:
                            lib_regen, additional_msgs = post_process_method(
                                assistant_response_obj, current_history_messages
                            )

                        if additional_msgs:
                            logger.info(f"库 {library.name} 返回了 {len(additional_msgs)} 条额外消息")
                            for msg_to_add in additional_msgs:
                                await self.conversation.add_message(msg_to_add) # Async
                                await self._send_message("full_message_object", self._serialize_message_for_payload(msg_to_add))
                        if lib_regen:
                            if stream_mode:
                                await self._send_message("info", "正在根据工作流验证结果重新生成回复...")
                            final_assistant_content_parts = []
                            needs_regeneration = True
                            break
                
                if needs_regeneration:
                    continue

                if workflow_lib.state.is_halted:
                    break

                assistant_content = assistant_response_obj.content if hasattr(assistant_response_obj, 'content') else None
                
                has_tool_calls = hasattr(assistant_response_obj, 'tool_calls') and assistant_response_obj.tool_calls
                
                if assistant_content: # Only append if there is content
                    final_assistant_content_parts.append(str(assistant_content))
                 
                # Reasoning content is sent via stream if stream_mode is on.
                # If not stream_mode, and there was reasoning, send it now.
                if hasattr(assistant_response_obj, 'reasoning_content') and assistant_response_obj.reasoning_content and not stream_mode:
                    await self._send_message("reasoning", assistant_response_obj.reasoning_content)
                
                # if not has_tool_calls:
                #     # If there was any assistant content (even if now None due to regeneration or empty from model)
                #     # and no tool calls, this is the final text response.
                #     # We must add the assistant_response_obj to history, even if content is None, if it came from model.
                #     if isinstance(assistant_response_obj, AssistantMessage): # Ensure it's an AssistantMessage
                #         await self.conversation.add_message(assistant_response_obj) # Async
                #         await self._send_message("full_message_object", self._serialize_message_for_payload(assistant_response_obj))
                #     break # Exit loop
                
                await self.conversation.add_message(assistant_response_obj) # Async
                await self._send_message("full_message_object", self._serialize_message_for_payload(assistant_response_obj))
                
                # Process tool calls
                if has_tool_calls:
                    await self._process_multiple_tool_calls(assistant_response_obj.tool_calls) # Async

                # 检查工作流状态是否变为等待用户输入
                if workflow_lib and workflow_lib.state.workflow_name:
                    current_step = workflow_lib.state.get_current_step()
                    if isinstance(current_step, UserInputStep) or workflow_lib.state.is_waiting_for_user:
                        # 工作流现在处于等待用户输入状态，应该停止生成
                        logger.info(f"工作流 '{workflow_lib.state.workflow_name}' 在处理工具调用后等待用户输入，停止生成")
                        break
            
            # conversation.count_tokens is async
            final_token_count = await self.conversation.count_tokens(self.model)
            if final_token_count > 0:
                await self._send_message("info", f"最终token数: {final_token_count}")
                logger.info(f"对话 {self.conversation_id} 的token数: {final_token_count}")
            
            return "\n".join(final_assistant_content_parts) if final_assistant_content_parts else None

        except Exception as e_process:
            error_msg = f"处理用户输入时出错: {traceback.format_exc()}"
            await self._send_message("error", error_msg)
            return f"发生错误，无法完成请求: {str(e_process)}"

    async def _process_multiple_tool_calls(self, tool_calls: List[ToolCall]) -> None: # Return is None
        """
        批量并发处理多个工具调用。结果直接添加到历史记录。
        
        参数:
            tool_calls: 工具调用对象列表
        """
        tool_call_tasks = [self._execute_single_tool(tc) for tc in tool_calls]
        tool_results_with_call_id = await asyncio.gather(*tool_call_tasks, return_exceptions=True)
        
        for i, result_or_exception in enumerate(tool_results_with_call_id):
            original_tool_call = tool_calls[i]
            tool_call_id_for_msg = original_tool_call.id
            tool_name_for_msg = original_tool_call.function.name
            
            result_content_str: str
            if isinstance(result_or_exception, Exception):
                error_msg = f"执行工具 {tool_name_for_msg} (ID: {tool_call_id_for_msg}) 时出错: {result_or_exception}"
                logger.error(f"{error_msg} Traceback: {traceback.format_tb(result_or_exception.__traceback__)}")
                await self._send_message("error", error_msg)
                result_content_str = dumps({"success": False, "error": str(result_or_exception)})
            else:
                # result_or_exception is the actual tool output here
                result_content_str = result_or_exception if isinstance(result_or_exception, str) else dumps(result_or_exception)
            
            truncated_result_str = self._truncate_string(result_content_str, config.TOOL_RESULT_MAX_LENGTH, config.TRUNCATION_SUFFIX)
            
            tool_message = ToolMessage(truncated_result_str, tool_call_id_for_msg)
            await self.conversation.add_message(tool_message) # Async
            await self._send_message("full_message_object", self._serialize_message_for_payload(tool_message))
        
    async def _execute_single_tool(self, tool_call: ToolCall) -> Any:
        """
        执行单个工具调用并返回结果。异常将由调用者（_process_multiple_tool_calls）处理。
        
        参数:
            tool_call: 工具调用对象
            
        返回:
            工具执行结果（原始格式，可能是str或dict/list等）
        """
        tool_name = tool_call.function.name
        tool_args_str = tool_call.function.arguments
        tool_id = tool_call.id # For messaging
        
        try:
            tool_args = json.loads(tool_args_str)
        except json.JSONDecodeError:
            warning_msg = f"无法解析工具参数 (ID: {tool_id}, Name: {tool_name}): {tool_args_str}"
            await self._send_message("warning", warning_msg)
            # Propagate error so it becomes a ToolMessage with error content
            raise ValueError(f"无法解析工具 {tool_name} 的参数: {tool_args_str}")
            
        await self._send_message("tool_call", {
            "name": tool_name,
            "args": tool_args,
            "id": tool_id
        })
        
        try:
            # library.execute_tool is async
            result = await self.execute_tool(tool_name, tool_args)
            
            # Prepare result for sending message (can be string or complex type)
            result_for_message_str = result if isinstance(result, str) else dumps(result)
            truncated_result_for_msg = self._truncate_string(result_for_message_str, config.TOOL_RESULT_MAX_LENGTH, config.TRUNCATION_SUFFIX)

            await self._send_message("tool_result", {
                "name": tool_name,
                "result": truncated_result_for_msg,
                "id": tool_id
            })
            return result # Return the original, non-truncated, non-stringified result
        except Exception as e_tool_exec:
            # Error already sent by self.execute_tool if it has _send_message
            # But we need to propagate for _process_multiple_tool_calls
            logger.error(f"执行工具 {tool_name} (ID: {tool_id}) 时在 _execute_single_tool 捕获到错误: {e_tool_exec}")
            raise # Re-raise for gather to catch and wrap

    async def execute_tool(self, tool_name: str, params: Dict[str, Any]) -> Any:
        """
        执行工具

        参数:
            tool_name: 工具名称（应为"库名.工具名"格式）
            params: 工具参数字典

        返回:
            工具执行结果 (原始类型)
        """
        logger.debug(f"尝试执行工具: {tool_name}, 参数: {params}")
        
        if "." not in tool_name:
            error_msg = f"工具名称 {tool_name} 格式不正确，应为'库名.工具名'格式"
            logger.error(error_msg)
            await self._send_message("error", error_msg)
            # Return a dict that can be serialized by dumps
            return {"success": False, "error": error_msg}
            
        library_name, tool_short_name = tool_name.split(".", 1)
        logger.debug(f"解析工具名: 库={library_name}, 工具={tool_short_name}")
        
        if library_name not in self.libraries:
            error_msg = f"找不到库: {library_name}"
            logger.error(error_msg)
            await self._send_message("warning", error_msg)
            return {"success": False, "error": error_msg}
            
        library = self.libraries[library_name]
        # library.tools is a dict mapping full tool name to method, not just short name
        # library.get_tools() returns list of schemas. We need to call the actual method.
        
        try:
            # library.execute_tool is async
            result = await library.execute_tool(tool_name, params) 
            logger.debug(f"工具 {tool_name} 执行结果: {type(result)}") # Log type of result
            return result
        except Exception as e_lib_exec:
            error_msg = f"执行工具 {tool_name} 时出错: {traceback.format_exc()}"
            logger.error(error_msg)
            await self._send_message("error", error_msg)
            return {
                "success": False,
                "error": f"执行工具 {tool_name} 失败: {str(e_lib_exec)}",
            }

    async def get_conversation_data_for_api(self) -> Dict[str, Any]: # Renamed & Async
        """
        获取完整的、可序列化的对话历史，用于API响应。

        返回:
            Dict[str, Any]: 包含 "history" 键，其值为消息列表的字典。
        """
        # conversation.get_serializable_messages 是异步的
        messages_list = await self.conversation.get_serializable_messages()
        return {"history": messages_list}

    async def get_all_libraries_details(self) -> List[Dict[str, Any]]:
        """
        获取所有已加载库的详细信息。

        返回:
            List[Dict[str, Any]]: 每个库的详细信息字典列表。
        """
        all_details = []
        for library_name, library_instance in self.libraries.items():
            if hasattr(library_instance, 'get_library_details') and callable(library_instance.get_library_details):
                try:
                    details_method = getattr(library_instance, 'get_library_details')
                    if asyncio.iscoroutinefunction(details_method):
                        details = await details_method()
                    else:
                        details = details_method() # Call sync method
                    all_details.append(details)
                except Exception as e_details:
                    logger.error(f"获取库 '{library_name}' 的详细信息时出错: {e_details}")
                    all_details.append({
                        "name": library_name,
                        "error": f"获取详细信息失败: {str(e_details)}"
                    })
            else:
                 all_details.append({
                    "name": library_name,
                    "error": "库不支持 get_library_details 方法"
                })
        return all_details

    async def close(self):
        """关闭Agent特定库的连接（共享MCP库由AgentManager管理）"""
        close_tasks = []
        shared_library_names = set(self.initialized_mcp_libraries.keys())
        
        logger.info(f"Agent {self.conversation_id or '[NoConvID]'} 开始关闭特定库...") # 添加日志

        for lib_name, library in self.libraries.items():
            if lib_name in shared_library_names:
                logger.debug(f"Agent {self.conversation_id}: 跳过关闭共享库 {lib_name}，由 AgentManager 管理") # 添加日志
                continue
                
            if hasattr(library, 'close') and callable(library.close):
                logger.debug(f"Agent {self.conversation_id}: 准备关闭 Agent 特定库: {lib_name}") # 添加日志
                close_method = getattr(library, 'close')
                
                # 诊断日志：检查关闭前的循环状态
                loop_before_closed = True # 假设循环最初是好的，除非我们不能得到它
                try:
                    loop_before = asyncio.get_event_loop_policy().get_event_loop()
                    loop_before_closed = loop_before.is_closed()
                    logger.debug(f"Agent {self.conversation_id}: 事件循环 (ID: {id(loop_before)}) 在关闭库 '{lib_name}' 前的状态: is_closed={loop_before_closed}")
                except RuntimeError as e_get_loop: 
                    logger.warning(f"Agent {self.conversation_id}: 在关闭库 '{lib_name}' 前获取事件循环失败: {e_get_loop}")
                    # loop_before_closed 保持 True (或可以设为未知状态)
                
                if asyncio.iscoroutinefunction(close_method):
                    close_tasks.append(close_method()) # Append awaitable
                else:
                    try:
                        close_method() # Call sync close
                        logger.info(f"Agent {self.conversation_id}: 同步关闭 Agent 特定库 {lib_name} 完成") # 添加日志
                    except Exception as e_sync_close:
                        logger.error(f"Agent {self.conversation_id}: 同步关闭库 {lib_name} 时发生错误: {e_sync_close}") # 添加日志

                # 诊断日志：检查关闭后的循环状态
                try:
                    loop_after = asyncio.get_event_loop_policy().get_event_loop()
                    loop_after_closed = loop_after.is_closed()
                    logger.debug(f"Agent {self.conversation_id}: 事件循环 (ID: {id(loop_after)}) 在关闭库 '{lib_name}' 后的状态: is_closed={loop_after_closed}")
                    if loop_after_closed and not loop_before_closed: 
                        logger.error(f"Agent {self.conversation_id}: 严重警告！库 '{lib_name}' 的关闭操作似乎导致了事件循环被关闭！")
                except RuntimeError as e_get_loop_after:
                     logger.error(f"Agent {self.conversation_id}: 在关闭库 '{lib_name}' 后获取事件循环失败: {e_get_loop_after}. 事件循环可能已被关闭。")


        if close_tasks:
            logger.debug(f"Agent {self.conversation_id}: 等待 {len(close_tasks)} 个异步库关闭操作...") # 添加日志
            results = await asyncio.gather(*close_tasks, return_exceptions=True)
            for i, result in enumerate(results): 
                if isinstance(result, Exception):
                    # 此处日志需要改进以关联到具体的库名
                    # 可以通过将 (lib_name, task) 存储在列表中，然后在结果中查找
                    logger.error(f"Agent {self.conversation_id}: 异步关闭某个库时发生错误: {result}") # 添加日志
                    
        logger.info(f"Agent {self.conversation_id or '[NoConvID]'} 已完成特定库的关闭流程。") # 添加日志

    async def update_output_connection(self, new_connection: Optional[Connection]) -> None:
        """
        更新Agent的输出连接
        
        参数:
            new_connection: 新的输出连接，如果为None则只清除现有连接
        """
        old_connection = self.output_connection
        self.output_connection = new_connection
        logger.debug(f"Agent {self.conversation_id} 的输出连接已更新: {old_connection} -> {new_connection}")
        
        # 如果新连接不为空，发送一个连接成功消息
        if new_connection:
            await self._send_message("connection_update", {"status": "connected", "agent_id": self.conversation_id})

    async def clone_for_new_conversation(self, new_conversation_id: str, new_connection: Optional[Connection] = None) -> 'Agent':
        """
        基于当前Agent创建一个新的Agent实例，复用已加载的资源
        
        参数:
            new_conversation_id: 新对话ID
            new_connection: 新对话的输出连接
            
        返回:
            新的Agent实例
        """
        from agent_manager import AgentManager
        agent_manager = await AgentManager.get_instance()
        
        # 创建新的对话实例
        new_conversation = Conversation(new_conversation_id)
        
        logger.info(f"正在从Agent {self.conversation_id} 克隆新的Agent {new_conversation_id}...")
        
        # 创建新的Agent实例，但复用已加载的库类和MCP库
        new_agent = Agent(
            model=self.model,  # 复用模型实例
            conversation=new_conversation,
            output_connection=new_connection,
            available_library_classes=self.available_library_classes,  # 复用库类定义
            library_config=self.library_config,  # 复用库配置
            initialized_mcp_libraries=self.initialized_mcp_libraries,  # 复用已初始化的MCP库
            agent_config=self.agent_config,  # 复用Agent配置
        )
        
        # 发送接入消息
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        join_content = f"用户 {new_conversation_id} 已接入，当前时间：{now}"
        join_message = UserMessage(content=join_content)
        # 添加到历史记录
        await new_agent.conversation.add_message(join_message)
        
        # 注册到管理器
        agent_manager.agents[new_conversation_id] = new_agent
        logger.info(f"基于预热Agent成功克隆新Agent: {new_conversation_id}")
        
        return new_agent



