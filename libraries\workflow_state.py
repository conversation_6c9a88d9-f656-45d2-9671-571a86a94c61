"""
工作流状态管理模块。

该模块定义了 `WorkflowState` 类，这是一个核心的 Pydantic 模型，用于集中管理和追踪
MeowAgent 框架中工作流的运行时状态。它被设计为同时支持两种主要的工作流类型：
1.  **YAML 定义的工作流**：基于静态的 YAML 文件描述工作流的结构和步骤。
2.  **Python 定义的工作流**：通过 Python 异步生成器动态生成工作流步骤。

`WorkflowState` 类的主要职责和功能包括：

一、状态存储与隔离：
    -   `workflow_name`: 当前活动工作流的名称。
    -   `source_type`: 明确标记工作流的来源是 'yaml' 还是 'python'。
    -   `workflow_data`: 对于 YAML 工作流，存储解析后的 `WorkflowDefinition` 对象。
        对于 Python 工作流，此字段通常为 None 或仅包含元数据，因为步骤由生成器提供。

二、YAML 工作流特定状态：
    -   `step_index`: 记录 YAML 工作流当前执行到的步骤在其定义列表中的索引。

三、Python 工作流特定状态：
    -   `generator_instance`: 存储活动的 Python 异步生成器实例 (`AsyncGenerator[WorkflowStep, Any]`)。
        此字段在序列化时会被排除，以避免复杂性。
    -   `current_yielded_step`: 缓存 Python 工作流生成器最近 `yield` 的 `WorkflowStep` 对象。
    -   `is_waiting_for_user`: 布尔标志，指示 Python 工作流当前是否已 `yield` 一个 `UserInputStep`
        并正在等待外部的用户输入。
    -   `is_halted`: 布尔标志，指示 Python 工作流是否已执行完毕 (生成器已耗尽) 或被明确终止。

四、通用状态：
    -   `registers`: 一个字典，用作工作流的通用数据存储（寄存器），允许在不同步骤间传递和修改数据。
    -   `action_group_call_counts`: 嵌套字典，用于追踪 `ExecuteStep` 中定义的动作组的执行次数。
        这对于控制循环执行或限制特定操作的调用频率非常重要。
        键可以是步骤索引 (YAML) 或步骤名称 (Python)。

五、核心方法与逻辑：
    -   `clear()`: 重置整个工作流状态，将其恢复到初始空状态。
    -   `get_current_step()`: 根据 `source_type` 和相应状态 (如 `step_index` 或 `current_yielded_step`)
        获取当前活动的 `WorkflowStep` 对象。
    -   初始化与切换：
        -   `set_yaml_workflow(name, data)`: 初始化或切换到一个新的 YAML 工作流，加载其定义和初始寄存器。
        -   `set_python_workflow(name, python_source)`: 初始化或切换到一个新的 Python 工作流，
            创建其异步生成器实例并准备好执行第一个步骤。
    -   步骤推进：
        -   `advance_step(next_index)`: (YAML) 将工作流状态推进到指定的下一个步骤索引。
        -   `advance_python_step_next()`: (Python) 通过调用异步生成器的 `__anext__()` 方法获取并处理下一个步骤。
            如果生成器 `yield` 一个 `UserInputStep`，会更新 `is_waiting_for_user` 状态。
        -   `advance_python_step_send(value)`: (Python) 通过调用异步生成器的 `asend(value)` 方法向其发送一个值
            (通常是用户输入或条件判断结果) 并获取下一个步骤。
    -   寄存器与计数器管理：
        -   `update_register(name, value)`: 更新指定寄存器的值。
        -   `increment_action_group_count(action_group_index)`: 增加当前 `ExecuteStep` 中特定动作组的调用计数。
        -   `reset_current_step_action_counts()`: 重置当前 `ExecuteStep` 所有动作组的调用计数，通常在进入新步骤或
            步骤逻辑重置时调用。

`WorkflowState` 通过 Pydantic 进行数据校验，确保状态的一致性和类型安全。
它与 `WorkflowDefinition`、各种 `WorkflowStep` 子类型 (如 `ExecuteStep`, `UserInputStep`)
以及 `PythonWorkflowSource` 紧密协作，共同构成了 MeowAgent 工作流执行引擎的基础。
"""

from typing import Dict, Any, Optional, Literal, Union
# 导入 TypeAlias 和 AsyncGenerator
from typing import TypeAlias, AsyncGenerator
from pydantic import BaseModel, Field, field_validator
# 导入 WorkflowStep 类型、PythonWorkflowSource 和 UserInputStep
from libraries.workflow_models import WorkflowDefinition, ExecuteStep, WorkflowStep, PythonWorkflowSource, UserInputStep
# 导入 logger
from log import logger
# 导入 UserMessage
from message import UserMessage
import traceback

# 定义 Python 工作流使用的异步生成器类型
AsyncGeneratorType: TypeAlias = AsyncGenerator[WorkflowStep, Any]

class WorkflowState(BaseModel):
    """
    工作流状态模型，用于集中管理工作流的运行时状态。
    支持 YAML 和 Python 两种类型的工作流。
    """
    # --- 核心状态字段 ---
    workflow_name: Optional[str] = None

    # --- 工作流来源与定义 ---
    source_type: Optional[Literal['yaml', 'python']] = Field(None, description="工作流来源类型 ('yaml' 或 'python')")
    # 对于 YAML，存储完整的 WorkflowDefinition；对于 Python，可为空或只存元数据 (例如描述和初始寄存器)
    # 注意：对于 Python 工作流，这个字段可能不会被直接使用，因为步骤来自生成器
    workflow_data: Optional[WorkflowDefinition] = Field(None, description="YAML工作流定义或Python工作流元数据(可选)")

    # --- YAML 工作流特定状态 ---
    # 标记 YAML 工作流当前步骤索引
    step_index: Optional[int] = Field(None, description="当前步骤索引（主要用于YAML）")

    # --- Python 工作流特定状态 ---
    # 存储 Python 工作流的活动生成器实例 (使用 AsyncGeneratorType)
    generator_instance: Optional[AsyncGeneratorType] = Field(None, description="Python 工作流的活动异步生成器实例", exclude=True) # exclude=True 避免序列化问题
    # 存储 Python 工作流最后 yield 的步骤对象
    current_yielded_step: Optional[WorkflowStep] = Field(None, description="Python 工作流当前 yield 的步骤")
    # 标记是否正在等待用户输入
    is_waiting_for_user: bool = Field(False, description="标记当前是否正在等待用户输入")
    # 标记工作流是否已终止
    is_halted: bool = Field(False, description="标记工作流是否已经终止")

    # --- 通用状态 ---
    registers: Dict[str, Any] = Field(default_factory=dict, description="工作流寄存器")
    # 记录 EXECUTE 步骤中动作组的调用次数 (同时适用于 YAML 和 Python 的 ExecuteStep)
    action_group_call_counts: Dict[Union[int, str], Dict[int, int]] = Field(default_factory=dict, description="动作组调用计数 {step_id: {group_idx: count}}, step_id 为 index (YAML) 或 name (Python)")

    # Pydantic 配置，允许任意类型用于生成器
    class Config:
        arbitrary_types_allowed = True

    def get_current_step(self) -> Optional[WorkflowStep]:
        """
        获取当前活动的工作流步骤对象。
        根据 source_type 从不同来源获取。
        """
        if self.source_type == 'python':
            # 对于 Python 工作流，返回最后 yield 的步骤
            return self.current_yielded_step
        elif self.source_type == 'yaml':
            # 对于 YAML 工作流，根据 step_index 获取步骤
            if self.workflow_data and self.step_index is not None:
                # 确保 workflow_data 是 WorkflowDefinition
                if isinstance(self.workflow_data, WorkflowDefinition):
                     return self.workflow_data.get_step_by_index(self.step_index)
                else:
                     logger.error(f"状态的 source_type 是 yaml 但 workflow_data 不是 WorkflowDefinition: {type(self.workflow_data)}")
                     return None
        # 其他情况（未设置工作流或类型未知）返回 None
        return None

    def clear(self) -> None:
        """重置所有状态字段为初始值"""
        logger.debug("正在清空工作流状态。")
        self.workflow_name = None
        self.source_type = None # 清空来源类型
        self.workflow_data = None
        self.step_index = None
        self.registers = {}
        self.action_group_call_counts = {}
        # 确保清除 Python 特定字段
        self.generator_instance = None # 清空生成器实例
        self.current_yielded_step = None # 清空 yield 的步骤
        self.is_waiting_for_user = False  # 重置等待用户输入标志
        self.is_halted = False  # 重置终止标志

    # --- 状态更新方法 --- 
    # --- YAML 工作流方法 ---
    def set_yaml_workflow(self, name: str, data: WorkflowDefinition) -> None:
        """
        设置新的 YAML 工作流并初始化状态。
        """
        logger.info(f"设置 YAML 工作流: {name}")
        self.clear() # 先清空所有状态
        self.source_type = 'yaml'
        self.workflow_name = name
        self.workflow_data = data
        first_step = data.get_first_step()
        self.step_index = first_step.index if first_step else 0
        self.registers = data.registers.copy()
        # 重置并初始化 action_group_call_counts
        self.action_group_call_counts = {}
        for step in data.get_parsed_steps():
            if isinstance(step, ExecuteStep) and step.index is not None:
                step_id = step.index # YAML 使用 index 作为 key
                self.action_group_call_counts[step_id] = {}
                for i, _ in enumerate(step.actions):
                    self.action_group_call_counts[step_id][i] = 0
        logger.info(f"YAML 工作流 '{name}' 状态已初始化。当前步骤索引: {self.step_index}")
        # 检查初始步骤 (仅记录)
        current_step = self.get_current_step()
        if not current_step:
             logger.warning(f"YAML 工作流 '{name}' 的初始步骤 ({self.step_index}) 无法获取。")

    def advance_step(self, next_index: int) -> None:
        """
        (YAML 工作流) 前进到下一步并重置计数器。
        """
        if self.source_type != 'yaml':
            logger.error(f"尝试在一个非 YAML 工作流 ({self.source_type}) 上调用 advance_step (YAML 特定方法)。")
            raise TypeError("advance_step 仅适用于 YAML 工作流。")

        # 检查 workflow_data 是否有效
        if not self.workflow_data or not isinstance(self.workflow_data, WorkflowDefinition):
            logger.error(f"无法推进 YAML 步骤：workflow_data 缺失或无效。")
            return

        target_step = self.workflow_data.get_step_by_index(next_index)
        if not target_step:
             logger.error(f"[严重] 无法推进 YAML 步骤：在工作流 '{self.workflow_name}' 中找不到目标步骤索引 {next_index}。")
             # 如何处理？可以选择停留在原地或清除状态。暂时停留在原地。
             # raise ValueError(f"找不到目标步骤索引 {next_index}。")
             return
             
        logger.debug(f"YAML 工作流 '{self.workflow_name}' 从步骤 {self.step_index} 前进到 {next_index}")
        self.step_index = next_index
        self.reset_current_step_action_counts() # 重置新步骤的计数器

    # --- Python 工作流方法 --- 
    def set_python_workflow(self, name: str, python_source) -> None:
        """
        设置新的 Python 工作流并初始化状态。
        会创建生成器实例并尝试获取第一个步骤。
        """
        logger.info(f"设置 Python 工作流: {name}")
        self.clear() # 先清空所有状态
        self.source_type = 'python'
        self.workflow_name = name
        # Python 工作流的 workflow_data 可以为空或只存储元数据，这里暂时设为 None
        # 如果需要存储描述等，可以在 WorkflowLibrary._set_workflow_internal 中创建一个临时的 WorkflowDefinition
        self.workflow_data = None 
        self.registers = python_source.initial_registers.copy()
        # Python 工作流不使用 step_index
        self.step_index = None
        
        # 创建异步生成器实例
        try:
            # 检查是否为远程工作流源
            from libraries.workflow_server import RemoteWorkflowSource
            if isinstance(python_source, RemoteWorkflowSource):
                # 远程工作流需要生成一个唯一的实例 ID
                import uuid
                workflow_instance_id = f"remote_{uuid.uuid4().hex[:12]}"
                logger.info(f"为远程工作流 '{name}' 生成实例 ID: {workflow_instance_id}")

                # 使用工厂函数创建生成器
                generator_factory = python_source.generator_function_factory(workflow_instance_id)
                self.generator_instance = generator_factory()
                logger.debug(f"远程工作流 '{name}' 的代理生成器实例已创建。")
            else:
                # 普通的 Python 工作流
                self.generator_instance = python_source.generator_function()
                logger.debug(f"Python 工作流 '{name}' 的生成器实例已创建。")

            # 尝试获取第一个步骤 (需要异步执行)
            # 这个操作应该在调用者 (WorkflowLibrary) 的异步上下文中完成
            # 这里只设置状态，不执行 next
            self.current_yielded_step = None
            self.is_waiting_for_user = False # 确保初始状态不是等待用户
            self.is_halted = False  # 确保初始状态不是终止状态
            # 初始化 Python 步骤的 action_group_call_counts (当第一个 ExecuteStep yield 时)
            self.action_group_call_counts = {}
        except Exception as e:
            logger.error(f"为 '{name}' 创建 Python 工作流生成器失败: {e}")
            self.clear() # 创建失败，清空状态
            raise # 重新抛出异常，让调用者知道失败了
        
        logger.info(f"Python 工作流 '{name}' 状态已初始化。生成器已创建。等待第一个步骤。")

    async def advance_python_step_next(self) -> None:
        """
        (Python 工作流) 通过调用 anext() 推进到生成器的下一步。
        更新 current_yielded_step。
        
        如果遇到 UserInputStep，则设置 is_waiting_for_user 为 True。
        """
        if self.source_type != 'python' or self.generator_instance is None:
            logger.error(f"无法推进 Python 步骤 (next)：不是 Python 工作流或生成器丢失。")
            raise TypeError("advance_python_step_next 仅适用于活动的 Python 工作流。")

        logger.debug(f"Python 工作流 '{self.workflow_name}' 正在推进 (next)。当前步骤: {self.current_yielded_step.name if self.current_yielded_step else '无'}")
        try:
            # 使用 anext 获取下一个步骤
            next_step = await self.generator_instance.__anext__()
            
            # 验证 yield 的值是 WorkflowStep
            if not isinstance(next_step, WorkflowStep):
                 logger.error(f"Python 工作流 '{self.workflow_name}' yield 了一个无效类型: {type(next_step)}。应为 WorkflowStep。")
                 # 可以选择抛出错误或停留在当前步骤。这里抛出错误。
                 raise TypeError(f"Python 工作流 yield 了非 WorkflowStep 对象: {type(next_step)}")
                 
            self.current_yielded_step = next_step
            
            # 检查是否为 UserInputStep 并设置等待状态
            if isinstance(next_step, UserInputStep):
                self.is_waiting_for_user = True
                logger.debug(f"Python 工作流 '{self.workflow_name}' 前进到输入等待步骤: {self.current_yielded_step.name}，标记等待用户输入")
            else:
                self.is_waiting_for_user = False
                logger.debug(f"Python 工作流 '{self.workflow_name}' 前进到步骤: {self.current_yielded_step.name}")
                
            self.reset_current_step_action_counts() # 重置新步骤的计数器
            
        except StopAsyncIteration:
            logger.info(f"Python 工作流 '{self.workflow_name}' 已完成 (StopAsyncIteration)。")
            workflow_name = self.workflow_name  # 保存工作流名称
            self.generator_instance = None
            self.current_yielded_step = None
            self.is_waiting_for_user = False
            self.is_halted = True
            # 重新抛出，但附带工作流名称信息供上层处理
            raise StopAsyncIteration(workflow_name)
        except Exception as e:
             logger.error(f"推进 Python 工作流 '{self.workflow_name}' (next) 时出错: {e}")
             # 保留当前状态？或者清除？暂时保留并重新抛出
             raise

    async def advance_python_step_send(self, value_to_send: Any) -> None:
        """
        (Python 工作流) 通过调用 asend() 向生成器发送值并推进到下一步。
        
        主要用于两种场景:
        1. 处理 ConditionStep 的结果 (发送 bool 值)
        2. 处理 UserInputStep 后的用户输入 (发送 UserMessage 对象)
        
        如果当前正在等待用户输入 (is_waiting_for_user=True)，并且 value_to_send 是 
        UserMessage 类型，则将用户输入发送到生成器并获取下一步。
        
        发送值后，如果下一步不是UserInputStep，将自动持续执行到下一个UserInputStep或工作流结束。
        """
        if self.source_type != 'python' or self.generator_instance is None:
            logger.error(f"无法推进 Python 步骤 (send)：不是 Python 工作流或生成器丢失。")
            raise TypeError("advance_python_step_send 仅适用于活动的 Python 工作流。")

        # 检查是否正在处理用户输入
        is_user_input = self.is_waiting_for_user and isinstance(value_to_send, UserMessage)
        
        logger.debug(f"Python 工作流 '{self.workflow_name}' 正在推进 (send: {'用户输入' if is_user_input else value_to_send})。"
                    f"当前步骤: {self.current_yielded_step.name if self.current_yielded_step else '无'}, "
                    f"等待状态: {self.is_waiting_for_user}")

        try:
            # 使用 asend 发送值并获取下一个步骤
            next_step = await self.generator_instance.asend(value_to_send)
            
            # 验证 yield 的值是 WorkflowStep
            if not isinstance(next_step, WorkflowStep):
                 logger.error(f"Python 工作流 '{self.workflow_name}' 在 send 后 yield 了一个无效类型: {type(next_step)}。应为 WorkflowStep。")
                 raise TypeError(f"Python 工作流在 send 后 yield 了非 WorkflowStep 对象: {type(next_step)}")

            self.current_yielded_step = next_step
            
            # 重置用户输入等待状态，然后检查新步骤是否为 UserInputStep
            self.is_waiting_for_user = False
            
            if isinstance(next_step, UserInputStep):
                self.is_waiting_for_user = True
                logger.debug(f"Python 工作流 '{self.workflow_name}' 在 send 后前进到输入等待步骤: {self.current_yielded_step.name}，标记等待用户输入")
            else:
                logger.debug(f"Python 工作流 '{self.workflow_name}' 在 send 后前进到步骤: {self.current_yielded_step.name}")
            
            self.reset_current_step_action_counts() # 重置新步骤的计数器
                
        except StopAsyncIteration:
            logger.info(f"Python 工作流 '{self.workflow_name}' 在 send 后完成 (StopAsyncIteration)。")
            workflow_name = self.workflow_name  # 保存工作流名称
            self.generator_instance = None
            self.current_yielded_step = None
            self.is_waiting_for_user = False
            self.is_halted = True
            # 重新抛出，但附带工作流名称信息供上层处理
            raise StopAsyncIteration(workflow_name)
        except Exception as e:
             error_detail = traceback.format_exc()
             logger.error(f"推进 Python 工作流 '{self.workflow_name}' (send) 时出错: {e}\n{error_detail}")
             raise

    # --- 通用状态操作方法 --- 
    def update_register(self, name: str, value: Any) -> None:
        """更新寄存器值"""
        logger.debug(f"更新寄存器 '{name}': {self.registers.get(name)} -> {value}")
        self.registers[name] = value

    def increment_action_group_count(self, action_group_index: int) -> None:
        """增加当前步骤中指定动作组的调用计数 (适用于 YAML 和 Python 的 ExecuteStep)"""
        current_step = self.get_current_step()
        # 确保是 ExecuteStep
        if not current_step or not isinstance(current_step, ExecuteStep):
            logger.warning(
                f"尝试增加动作组计数，但当前步骤不是 ExecuteStep。步骤: {current_step}"
            )
            return

        # 获取步骤标识符 (index for YAML, name for Python)
        # 注意：Python ExecuteStep 必须有 name
        if self.source_type == 'yaml':
            step_id = current_step.index
            if step_id is None:
                 logger.error(f"无法增加计数：YAML ExecuteStep 缺少 index。步骤: {current_step}")
                 return
        elif self.source_type == 'python':
             step_id = current_step.name
             if not step_id:
                  logger.error(f"无法增加计数：Python ExecuteStep 缺少 name。步骤: {current_step}")
                  return
        else:
            logger.error(f"无法增加计数：未知的 source type {self.source_type}")
            return

        # 初始化步骤的计数器 (如果不存在)
        if step_id not in self.action_group_call_counts:
            logger.warning(
                f"在 action_group_call_counts 中找不到步骤 {step_id}，正在初始化。"
            )
            self.action_group_call_counts[step_id] = {}
            # 初始化该步骤所有动作组的计数为0
            for i, _ in enumerate(current_step.actions):
                 self.action_group_call_counts[step_id][i] = 0
        
        step_group_counts = self.action_group_call_counts[step_id]

        # 验证动作组索引是否有效
        if not (0 <= action_group_index < len(current_step.actions)):
            logger.error(
                 f"尝试为步骤 {step_id} 中无效的动作组索引 {action_group_index} 增加计数。"
                 f"有效索引: 0-{len(current_step.actions)-1}"
             )
            return
        
        # 确保该组索引在计数器中 (理论上初始化时已完成)
        if action_group_index not in step_group_counts:
            logger.warning(f"步骤 {step_id} 的计数中缺少动作组索引 {action_group_index}，初始化为 0。")
            step_group_counts[action_group_index] = 0
            
        # 增加计数
        step_group_counts[action_group_index] += 1
        logger.debug(f"步骤 '{step_id}' 动作组 {action_group_index} 的调用计数增加到 {step_group_counts[action_group_index]}")


    def reset_current_step_action_counts(self) -> None:
        """重置当前步骤的所有动作组调用计数为0"""
        current_step = self.get_current_step()
        if not current_step or not isinstance(current_step, ExecuteStep):
            # 如果当前不是 EXECUTE 步骤，则无需重置
            return

        # 获取步骤标识符
        if self.source_type == 'yaml':
            step_id = current_step.index
            if step_id is None: return
        elif self.source_type == 'python':
             step_id = current_step.name
             if not step_id: return
        else: 
             return

        # 如果步骤存在于计数器中，重置其所有动作组的计数
        if step_id in self.action_group_call_counts:
            step_group_counts = self.action_group_call_counts[step_id]
            # 确保该步骤的所有预期动作组索引都存在于计数器中并重置
            action_group_indices_to_reset = set(range(len(current_step.actions)))
            # 重置已有的计数
            for group_index in list(step_group_counts.keys()):
                if group_index in action_group_indices_to_reset:
                    step_group_counts[group_index] = 0
                    action_group_indices_to_reset.remove(group_index)
                else:
                    # 计数器中存在无效的索引? 记录警告并移除
                    logger.warning(f"正在从步骤 {step_id} 的计数中移除意外的动作组索引 {group_index}")
                    del step_group_counts[group_index]
            # 添加并初始化缺失的预期索引
            for missing_index in action_group_indices_to_reset:
                 step_group_counts[missing_index] = 0
                 
            logger.debug(f"步骤 '{step_id}' 的 action_group_call_counts 已重置。")
        else:
            # 如果步骤首次遇到 (例如 Python 首次 yield ExecuteStep)，初始化计数器
            logger.debug(f"正在为步骤 '{step_id}' 初始化 action_group_call_counts。")
            self.action_group_call_counts[step_id] = {}
            for i, _ in enumerate(current_step.actions):
                 self.action_group_call_counts[step_id][i] = 0

    # Field validator to ensure generator_instance is not directly set via dict/json
    @field_validator('generator_instance', mode='before')
    @classmethod
    def block_generator_instance_from_dict(cls, v):
        # 检查是否为异步生成器
        if v is not None:
            import types
            if not isinstance(v, types.AsyncGeneratorType):
                raise ValueError("generator_instance 只能设置为异步生成器类型")
        return v 