"""
该模块定义了用于解析、验证和表示工作流（Workflow）的Pydantic数据模型。
它旨在提供一个结构化、类型安全的方式来处理工作流定义，无论是从YAML文件加载还是通过Python代码动态生成。

主要功能和组件包括：

1.  **`WorkflowOperation` (Enum)**:
    定义了工作流步骤中可能执行的各种操作类型，例如 `EXECUTE`（执行工具）、
    `CONDITION`（条件判断）、`GENERATE`（生成文本）、`JUMP`（跳转）、`USER_INPUT`（等待用户输入）等。

2.  **`ActionDefinition` (BaseModel)**:
    用于 `ExecuteStep`，定义了关于工具调用的约束，如允许调用的工具名称列表 (`names`)
    以及最小/最大调用次数 (`min_calls`, `max_calls`)。

3.  **`WorkflowStepBase` (BaseModel)**:
    所有具体工作流步骤模型的基类，包含通用字段如 `index`、`name`（可选）、
    `description`（可选）和 `operation`（操作类型）。

4.  **具体步骤模型 (继承自 `WorkflowStepBase`)**:
    -   `NopStep`: 无操作步骤，直接进入下一步。
    -   `ExecuteStep`: 执行一个或多个 `ActionDefinition` 中定义的工具/动作。
    -   `ConditionStep`: 根据条件表达式的结果，跳转到不同的分支 (`true_branch`, `false_branch`)。
    -   `JumpStep`: 无条件或有条件地跳转到工作流中的另一个步骤。
    -   `GenerateStep`: 指示模型直接生成文本内容，而不调用任何工具。
    -   `HaltStep`: 终止当前工作流的执行。
    -   `UserInputStep`: 暂停工作流，等待用户提供输入。

5.  **`WorkflowStep` (Union)**:
    一个联合类型，包含了所有可能的具体工作流步骤类型，方便类型注解和处理。

6.  **`StructuredExample` (BaseModel)**:
    用于定义结构化的工作流执行示例，包含用户输入和预期的步骤序列，
    可用于提示工程或测试。

7.  **`WorkflowDefinition` (BaseModel)**:
    表示一个完整的工作流定义（通常来自YAML文件）。它包含：
    -   `description`: 工作流的描述。
    -   `steps`: 原始步骤定义列表（初始为字典，后续可解析为具体的 `WorkflowStep` 对象）。
    -   `registers`: 工作流范围内的寄存器及其初始值。
    -   `prompt_examples_text` 和 `prompt_examples_structured`: 用于提示工程的文本或结构化示例。
    -   `history_examples`: 对话历史示例。
    它还提供了方法来解析步骤、按索引或名称获取步骤对象，以及管理步骤的显示名称。

8.  **`PythonWorkflowSource` (BaseModel)**:
    用于表示通过Python生成器函数定义的动态工作流。它包含：
    -   `generator_function`: 一个异步生成器函数，逐个产生 `WorkflowStep` 对象。
    -   `description`: Python工作流的描述。
    -   `initial_registers`: Python工作流的初始寄存器。

9.  **`WorkflowSource` (BaseModel)**:
    一个统一的包装模型，用于表示工作流的来源。它可以包含：
    -   `type`: 明确指示来源是 'yaml' 还是 'python'。
    -   `source`: 一个联合字段，其实际类型为 `WorkflowDefinition` (对于YAML) 或
        `PythonWorkflowSource` (对于Python)。
    该模型提供了一个统一的接口来访问工作流的 `description` 和 `initial_registers`，
    无论其原始定义方式如何。

这些Pydantic模型确保了工作流数据在加载、处理和执行过程中的一致性和有效性，
提高了代码的可维护性和健壮性，并为工作流的静态分析和动态执行提供了坚实的基础。
"""

from typing import Dict, List, Optional, Union, Any, Literal, Callable, AsyncGenerator
from pydantic import BaseModel, Field, model_validator, field_validator
from enum import Enum
import uuid
from log import logger

class WorkflowOperation(str, Enum):
    """
    工作流操作类型枚举
    
    定义了工作流步骤可以执行的各种操作类型：
    - EXECUTE: 执行指定的工具或动作
    - CONDITION: 条件判断，根据条件结果选择不同分支
    - GENERATE: 生成文本内容，不调用工具
    - NOP: 无操作，直接前进到下一步
    - JUMP: 跳转到指定步骤
    - HALT: 终止工作流
    - USER_INPUT: 等待用户输入
    - SWITCH: 根据条件选择不同的工作流分支
    """
    EXECUTE = "EXECUTE"    # 执行指定的工具或动作
    CONDITION = "CONDITION"  # 条件判断，根据条件结果选择不同分支
    GENERATE = "GENERATE"   # 生成文本内容，不调用工具
    NOP = "NOP"         # 无操作，直接前进到下一步
    JUMP = "JUMP"        # 跳转到指定步骤
    HALT = "HALT"        # 终止工作流
    USER_INPUT = "USER_INPUT" # 等待用户输入
    SWITCH = "SWITCH"    # 根据条件选择不同的工作流分支

class ActionDefinition(BaseModel):
    """动作定义，用于EXECUTE步骤中的工具调用约束组"""
    names: List[str] = Field(..., min_length=1) # 工具名或库名列表，确保至少有一个元素
    min_calls: int = 1
    max_calls: int = 1
    
    @model_validator(mode='before')
    @classmethod
    def validate_names_structure(cls, values):
        """
        验证 names 字段结构
        """
        if isinstance(values, dict):
            # 如果输入是字典
            if 'names' in values:
                # 验证 'names' 是列表且不为空
                if not isinstance(values['names'], list) or not values['names']:
                    raise ValueError("'names' 字段必须是非空列表")
                # 清理列表中的空字符串或None
                values['names'] = [name.strip() for name in values['names'] if name and name.strip()]
                if not values['names']:
                     raise ValueError("'names' 列表清理后为空")
            else:
                raise ValueError("动作定义必须包含 'names' 列表字段")
        else:
            raise ValueError("动作定义必须是字典格式，且包含 'names' 列表字段")
        return values
    
    @field_validator('names')
    @classmethod
    def validate_names_list(cls, v):
        """验证 names 列表及其内容"""
        if not isinstance(v, list) or not v:
            raise ValueError("'names' 必须是一个非空列表")
        for name in v:
            if not isinstance(name, str) or not name.strip():
                raise ValueError("names 列表中的每个名称都必须是非空字符串")
        return v
    
    @field_validator('min_calls', 'max_calls')
    @classmethod
    def validate_calls(cls, v, info):
        """验证调用次数有效"""
        field_name = info.field_name
        if field_name == 'min_calls':
            if v < 0:
                raise ValueError("min_calls不能小于0")
        elif field_name == 'max_calls':
            if v <= 0:
                raise ValueError("max_calls必须大于0")
            # 检查min_calls
            data = info.data
            if 'min_calls' in data and v < data['min_calls']:
                raise ValueError("max_calls不能小于min_calls")
        return v


class WorkflowStepBase(BaseModel):
    """工作流步骤的基类"""
    index: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None
    operation: WorkflowOperation
    
    @model_validator(mode='after')
    def generate_name_if_missing(self):
        """如果名称为空，则根据操作类型自动生成一个随机名称"""
        if not self.name:
            # 使用操作类型作为前缀，加上8位随机字符串
            operation_prefix = self.operation.value.lower()
            random_suffix = uuid.uuid4().hex[:8]
            self.name = f"{operation_prefix}_{random_suffix}"
        return self


class UserInputStep(WorkflowStepBase):
    """等待用户输入步骤，并将输入作为结果传递回去"""
    operation: Literal[WorkflowOperation.USER_INPUT] = WorkflowOperation.USER_INPUT
    # description 字段已在 WorkflowStepBase 中定义，这里可以不重复
    # prompt: Optional[str] = Field(None, description="提示用户输入的信息") # 可以考虑是否需要单独的 prompt 字段，或者复用 description


class NopStep(WorkflowStepBase):
    """无操作步骤，仅跳转到下一步"""
    operation: Literal[WorkflowOperation.NOP] = WorkflowOperation.NOP
    next: Optional[int] = None


class ExecuteStep(WorkflowStepBase):
    """执行动作步骤，调用指定的工具"""
    operation: Literal[WorkflowOperation.EXECUTE] = WorkflowOperation.EXECUTE
    actions: List[ActionDefinition] = Field(default_factory=list)
    next: Optional[int] = None
    
    @model_validator(mode='before')
    @classmethod
    def process_actions(cls, data):
        """处理actions字段，确保是有效的列表"""
        if isinstance(data, dict):
            # 确保actions是列表
            if not isinstance(data.get('actions'), list):
                data['actions'] = []
                
        return data


class ConditionStep(WorkflowStepBase):
    """条件判断步骤，根据条件结果选择不同分支"""
    operation: Literal[WorkflowOperation.CONDITION] = WorkflowOperation.CONDITION
    condition: Optional[str] = None
    condition_description: Optional[str] = None
    true_branch: Optional[int] = None
    false_branch: Optional[int] = None
    
    @model_validator(mode='after')
    def set_condition_description(self):
        """如果condition_description为空，优先使用description字段，其次使用condition字段"""
        if not self.condition_description:
            # 优先使用 description 字段
            if self.description:
                self.condition_description = self.description
            # 其次使用 condition 字段
            elif self.condition:
                self.condition_description = self.condition
            # 最后使用默认值
            else:
                self.condition_description = "未指定条件"
        return self


class JumpStep(WorkflowStepBase):
    """跳转步骤，可以是无条件跳转或条件跳转"""  
    operation: Literal[WorkflowOperation.JUMP] = WorkflowOperation.JUMP
    next: Optional[int] = None
    next_condition: Optional[str] = None
    condition_description: Optional[str] = None
    true_branch: Optional[int] = None
    false_branch: Optional[int] = None
    
    @model_validator(mode='after')
    def validate_jump_config(self):
        """验证跳转配置的一致性"""
        # 无条件跳转
        if self.next_condition is None:
            if self.next is None:
                raise ValueError("无条件跳转必须指定next字段")
        # 条件跳转
        else:
            if self.true_branch is None or self.false_branch is None:
                raise ValueError("条件跳转必须同时指定true_branch和false_branch")
            # 条件描述
            if not self.condition_description:
                self.condition_description = self.next_condition or "未指定条件"
        return self


class GenerateStep(WorkflowStepBase):
    """生成内容步骤，直接生成文本不调用工具"""
    operation: Literal[WorkflowOperation.GENERATE] = WorkflowOperation.GENERATE
    content_description: str = "生成相应的文本内容"
    wait_user: bool = False
    next: Optional[int] = None
    
    @model_validator(mode='after')
    def validate_generate_config(self):
        """验证生成配置的一致性"""
        return self


class HaltStep(WorkflowStepBase):
    """终止工作流步骤，不再继续执行"""
    operation: Literal[WorkflowOperation.HALT] = WorkflowOperation.HALT



class StructuredExample(BaseModel):
    """结构化的工作流示例"""
    title: Optional[str] = None
    user_input: str = ""
    steps: List[Dict[str, Any]] = Field(default_factory=list)


class CaseDefinition(BaseModel):
    """
    工作流分支选择案例定义。
    用于 SwitchStep 中定义可选择的不同分支。
    """
    name: str = Field(..., description="分支的名称标识符")
    description: str = Field(..., description="分支的描述，供 LLM 理解和选择")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """验证分支名称"""
        if not isinstance(v, str) or not v.strip():
            raise ValueError("分支名称必须是非空字符串")
        return v.strip()
    
    @field_validator('description')
    @classmethod  
    def validate_description(cls, v):
        """验证分支描述"""
        if not isinstance(v, str) or not v.strip():
            raise ValueError("分支描述必须是非空字符串")
        return v.strip()

class Case(BaseModel):
    """分支处理函数定义"""
    name: str = Field(..., description="分支的名称标识符")
    description: str = Field(..., description="分支的描述，供 LLM 理解和选择")
    handler: Callable = Field(..., description="目标工作流函数")

class SwitchStep(WorkflowStepBase):
    """
    工作流分支选择步骤。
    允许根据 LLM 的选择切换到不同的工作流分支。
    """
    operation: Literal[WorkflowOperation.SWITCH] = WorkflowOperation.SWITCH
    cases: List[CaseDefinition] = Field(..., min_length=1, description="可选择的分支列表")
    
    @field_validator('cases')
    @classmethod
    def validate_cases(cls, v):
        """验证分支列表"""
        if not isinstance(v, list) or len(v) == 0:
            raise ValueError("cases 必须是非空列表")
        
        # 检查分支名称是否重复
        names = [case.name for case in v]
        if len(names) != len(set(names)):
            raise ValueError("分支名称不能重复")
        
        return v 
    



# 所有可能的工作流步骤类型的联合
WorkflowStep = Union[
    NopStep,
    ExecuteStep,
    ConditionStep,
    JumpStep,
    GenerateStep,
    HaltStep,
    UserInputStep, 
    SwitchStep  
]


class WorkflowDefinition(BaseModel):
    """完整的工作流定义"""
    description: str
    steps: List[Dict[str, Any]]  # 使用Dict而不是WorkflowStep，因为步骤类型在加载后确定
    registers: Dict[str, Any] = Field(default_factory=dict)
    prompt_examples_text: Optional[str] = None
    prompt_examples_structured: Optional[List[StructuredExample]] = None
    history_examples: Optional[List[List[Dict[str, Any]]]] = None
    
    # 解析后的步骤对象缓存
    _parsed_steps: Optional[List[WorkflowStep]] = None
    
    def get_parsed_steps(self) -> List[WorkflowStep]:
        """获取解析后的步骤对象列表"""
        if self._parsed_steps is None:
            self._parsed_steps = []
            for step_dict in self.steps:
                # 根据operation确定具体步骤类型
                operation = step_dict.get("operation")
                if not operation:
                    continue
                    
                try:
                    if operation == WorkflowOperation.NOP:
                        self._parsed_steps.append(NopStep(**step_dict))
                    elif operation == WorkflowOperation.EXECUTE:
                        self._parsed_steps.append(ExecuteStep(**step_dict))
                    elif operation == WorkflowOperation.CONDITION:
                        self._parsed_steps.append(ConditionStep(**step_dict))
                    elif operation == WorkflowOperation.JUMP:
                        self._parsed_steps.append(JumpStep(**step_dict))
                    elif operation == WorkflowOperation.GENERATE:
                        self._parsed_steps.append(GenerateStep(**step_dict))
                    elif operation == WorkflowOperation.HALT:
                        self._parsed_steps.append(HaltStep(**step_dict))
                    elif operation == WorkflowOperation.USER_INPUT: # 新增 UserInputStep 解析
                        self._parsed_steps.append(UserInputStep(**step_dict))
                    elif operation == WorkflowOperation.SWITCH: # 新增 SwitchStep 解析
                        self._parsed_steps.append(SwitchStep(**step_dict))
                except Exception as e:
                    # 解析错误时记录日志，但不中断，保持健壮性
                    logger.error(f"解析步骤时出错: {e}, 步骤: {step_dict}")
            
        return self._parsed_steps
    
    def get_step_by_index(self, index: int) -> Optional[WorkflowStep]:
        """根据索引获取步骤对象"""
        if index is None:
            return None
            
        for step in self.get_parsed_steps():
            if step.index == index:
                return step
                
        return None
    
    def get_step_by_name(self, name: str) -> Optional[WorkflowStep]:
        """根据名称获取步骤对象"""
        if not name:
            return None
            
        for step in self.get_parsed_steps():
            if step.name == name:
                return step
                
        return None
    
    def get_step_display_name(self, step_or_identifier: Union[WorkflowStep, int, str]) -> str:
        """获取步骤的显示名称，优先使用name，其次是index。

        参数:
            step_or_identifier: 步骤对象、步骤索引(int)或步骤名称(str)。

        返回:
            步骤的友好显示名称。
        """
        if step_or_identifier is None:
            return "未知步骤"

        step: Optional[WorkflowStep] = None

        # 尝试根据输入类型获取步骤对象
        if isinstance(step_or_identifier, WorkflowStepBase):
            step = step_or_identifier
        elif isinstance(step_or_identifier, int):
            step = self.get_step_by_index(step_or_identifier)
        elif isinstance(step_or_identifier, str):
            # 尝试作为名称查找
            step = self.get_step_by_name(step_or_identifier)
            # 如果名称找不到，尝试作为数字索引查找
            if not step and step_or_identifier.isdigit():
                step = self.get_step_by_index(int(step_or_identifier))

        # 生成显示名称
        if step:
            # 优先使用 name
            if step.name:
                return step.name
            # 其次使用 index
            elif step.index is not None:
                return f"步骤{step.index}"

        # 如果无法确定步骤或步骤没有名称/索引，返回输入作为标识符
        return f"未知步骤({step_or_identifier})"
    
    def clear_parsed_cache(self) -> None:
        """清除已解析的步骤缓存，强制重新解析"""
        self._parsed_steps = None
        
    def get_first_step(self) -> Optional[WorkflowStep]:
        """获取第一个步骤（索引为0）"""
        return self.get_step_by_index(0)
        
    def get_register_default(self, register_name: str, default: Any = None) -> Any:
        """获取寄存器默认值，如不存在则返回默认值"""
        return self.registers.get(register_name, default)

# --- 新增 WorkflowSource 用于统一表示工作流来源 ---

class PythonWorkflowSource(BaseModel):
    """Python 工作流特定的元数据"""
    generator_function: Callable[[], AsyncGenerator[WorkflowStep, Any]] = Field(..., description="Python 工作流生成器函数")
    description: str = Field(default="", description="从 Python 文件加载的描述")
    initial_registers: Dict[str, Any] = Field(default_factory=dict, description="从 Python 文件加载的初始寄存器")

class WorkflowSource(BaseModel):
    """
    统一的工作流来源表示。
    用于区分 YAML 定义的工作流、Python 生成器定义的工作流和远程工作流。
    """
    type: Literal['yaml', 'python'] = Field(..., description="工作流来源类型")
    # 使用 Union 包含 WorkflowDefinition、PythonWorkflowSource 和 RemoteWorkflowSource
    source: Union[WorkflowDefinition, PythonWorkflowSource, 'RemoteWorkflowSource'] = Field(..., description="工作流的具体来源数据")

    # 提供统一访问接口，简化 WorkflowLibrary 中的使用
    @property
    def description(self) -> str:
        """获取工作流描述 (统一接口)"""
        if self.type == 'yaml':
            # 确保 self.source 是 WorkflowDefinition
            if isinstance(self.source, WorkflowDefinition):
                return self.source.description
            else:
                # 处理类型不匹配的异常情况
                logger.error(f"WorkflowSource 类型为 'yaml' 但 source 不是 WorkflowDefinition: {type(self.source)}")
                return "[类型错误]"
        elif self.type == 'python':
            # 确保 self.source 是 PythonWorkflowSource 或 RemoteWorkflowSource
            if isinstance(self.source, PythonWorkflowSource):
                return self.source.description
            else:
                # 检查是否为 RemoteWorkflowSource
                from libraries.workflow_server import RemoteWorkflowSource
                if isinstance(self.source, RemoteWorkflowSource):
                    return self.source.description
                else:
                    # 处理类型不匹配的异常情况
                    logger.error(f"WorkflowSource 类型为 'python' 但 source 不是 PythonWorkflowSource 或 RemoteWorkflowSource: {type(self.source)}")
                    return "[类型错误]"
        return "[未知类型]" # 理论上不应发生

    @property
    def initial_registers(self) -> Dict[str, Any]:
        """获取初始寄存器 (统一接口)"""
        if self.type == 'yaml':
             if isinstance(self.source, WorkflowDefinition):
                return self.source.registers
             else:
                return {} # 异常情况返回空字典
        elif self.type == 'python':
            if isinstance(self.source, PythonWorkflowSource):
                return self.source.initial_registers
            else:
                # 检查是否为 RemoteWorkflowSource
                from libraries.workflow_server import RemoteWorkflowSource
                if isinstance(self.source, RemoteWorkflowSource):
                    return self.source.initial_registers
                else:
                    return {} # 异常情况返回空字典
        return {} # 未知类型返回空字典


# 在文件末尾，当 RemoteWorkflowSource 被定义后，重建 WorkflowSource 模型
def rebuild_workflow_source_model():
    """重建 WorkflowSource 模型以支持前向引用"""
    try:
        WorkflowSource.model_rebuild()
        logger.debug("WorkflowSource 模型已重建以支持 RemoteWorkflowSource")
    except Exception as e:
        logger.warning(f"重建 WorkflowSource 模型时出错: {e}")

