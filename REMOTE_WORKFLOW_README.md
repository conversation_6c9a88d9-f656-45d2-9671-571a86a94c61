# MeowAgent 远程工作流功能

## 概述

MeowAgent 现在支持远程工作流功能，允许外部 Python 脚本通过 WebSocket 连接远程定义和驱动 MeowAgent 内部的工作流步骤。这个功能实现了一个 SDK，其中的 `WorkflowClient` 可以和本项目的 `WorkflowServer` 连接，最终功能和 `WorkflowContext` 完全等价。

## 核心特性

1. **完全兼容的 API**: `WorkflowClient` 提供与 `WorkflowContext` 完全相同的接口
2. **透明集成**: 对 `WorkflowLibrary` 和 `WorkflowExecutor` 透明，无需感知工作流是本地还是远程
3. **实时通信**: 通过 WebSocket 实现实时的双向通信
4. **动态加载**: 远程工作流可以像本地工作流一样被发现和管理

## 架构组件

### 服务器端 (MeowAgent)

1. **`libraries/workflow_server.py`**: 远程工作流服务器模块
   - `RemoteWorkflowConnectionManager`: 管理 WebSocket 连接和消息路由
   - `ProxyGenerator`: 代理生成器，桥接远程客户端和本地工作流引擎
   - `RemoteWorkflowSource`: 远程工作流源的数据模型

2. **`api.py`**: 新增 WebSocket 端点 `/ws/v1/remote_workflow_connector`

3. **`libraries/workflow_loader.py`**: 支持加载远程工作流标记文件

### 客户端 (SDK)

1. **`workflow_client.py`**: WorkflowClient SDK
   - `WorkflowClient`: 主要的客户端类
   - `WorkflowSession`: 工作流会话，提供与 `WorkflowContext` 相同的接口

## 使用方法

### 1. 创建远程工作流标记文件

在 `workflows/` 目录下创建一个 `.remote.yaml` 文件来标记远程工作流：

```yaml
# workflows/my_remote_flow.remote.yaml
remote_workflow_script_name: "my_remote_flow"
description: "示例远程工作流"
```

### 2. 编写远程工作流客户端

```python
import asyncio
from workflow_client import WorkflowClient

async def my_remote_workflow():
    # 创建客户端并连接
    client = WorkflowClient()
    await client.connect("ws://localhost:8000/ws/v1/remote_workflow_connector")
    
    # 启动工作流实例
    session = await client.start_workflow_instance("my_remote_flow")
    
    # 等待用户输入
    user_message = await session.user_input("请输入您的问题")
    
    # 执行搜索
    await session.execute(
        description="搜索相关信息",
        actions=[{"names": ["web_search"], "min_calls": 1, "max_calls": 3}]
    )
    
    # 条件判断
    is_complete = await session.condition("搜索结果是否充分？")
    
    # 生成回复
    await session.generate("根据搜索结果生成回复")
    
    # 结束工作流
    await session.halt()
    
    # 关闭连接
    await client.close()

if __name__ == "__main__":
    asyncio.run(my_remote_workflow())
```

### 3. 运行示例

1. 启动 MeowAgent 服务器：
   ```bash
   python api.py
   ```

2. 运行示例远程工作流：
   ```bash
   python example_remote_workflow.py
   ```

## API 参考

### WorkflowSession 方法

`WorkflowSession` 提供与 `WorkflowContext` 完全相同的方法：

- `execute(description, actions, name=None)`: 执行步骤
- `condition(description, name=None)`: 条件判断
- `generate(description, wait_user=False, name=None)`: 生成内容
- `user_input(description, name=None)`: 等待用户输入
- `switch(name, description, cases, default_handler=None)`: 分支选择
- `halt(name=None)`: 终止工作流

### 消息协议

#### 客户端到服务器

1. **启动工作流实例**:
   ```json
   {
     "type": "start_workflow_instance",
     "workflow_script_name": "my_remote_flow"
   }
   ```

2. **定义步骤**:
   ```json
   {
     "type": "define_step",
     "workflow_instance_id": "remote_abc123",
     "client_request_id": "req_xyz789",
     "step_definition": {
       "operation": "EXECUTE",
       "name": "search_step",
       "description": "搜索信息",
       "actions": [...]
     }
   }
   ```

#### 服务器到客户端

1. **工作流实例启动确认**:
   ```json
   {
     "type": "workflow_instance_started",
     "workflow_instance_id": "remote_abc123",
     "workflow_script_name": "my_remote_flow"
   }
   ```

2. **请求下一步骤定义**:
   ```json
   {
     "type": "request_next_step_definition",
     "workflow_instance_id": "remote_abc123"
   }
   ```

3. **步骤结果**:
   ```json
   {
     "type": "step_result",
     "workflow_instance_id": "remote_abc123",
     "client_request_id": "req_xyz789",
     "result": true
   }
   ```

## 技术实现细节

### 代理生成器机制

远程工作流通过代理生成器 (`ProxyGenerator`) 实现，它：

1. 实现标准的 `AsyncGenerator[WorkflowStep, Any]` 接口
2. 通过 WebSocket 与远程客户端通信
3. 将客户端发送的步骤定义转换为 `WorkflowStep` 对象
4. 将执行结果发送回客户端

### 连接管理

`RemoteWorkflowConnectionManager` 负责：

1. 管理 WebSocket 连接
2. 路由消息到正确的代理生成器
3. 维护工作流实例的生命周期
4. 处理连接断开和错误恢复

### 工作流加载

修改后的 `workflow_loader.py` 支持：

1. 识别 `.remote.yaml` 和 `.remote_wf.py` 标记文件
2. 创建 `RemoteWorkflowSource` 对象
3. 注册远程工作流到连接管理器

## 注意事项

1. **依赖**: 客户端需要安装 `websockets` 库
2. **网络**: 确保 WebSocket 连接稳定
3. **错误处理**: 实现适当的重连和错误恢复机制
4. **安全**: 在生产环境中考虑认证和授权

## 故障排除

### 常见问题

1. **连接失败**: 检查 MeowAgent 服务器是否运行在正确的端口
2. **工作流未注册**: 确保 `.remote.yaml` 文件格式正确
3. **消息超时**: 检查网络连接和服务器负载

### 调试技巧

1. 启用详细日志记录
2. 检查 WebSocket 消息流
3. 验证步骤定义格式
4. 监控服务器端代理生成器状态

## 实现完成情况

✅ **已完成的功能**:

1. **服务器端支持**:
   - `libraries/workflow_server.py`: 完整的远程工作流服务器模块
   - `RemoteWorkflowConnectionManager`: WebSocket 连接和消息管理
   - `ProxyGenerator`: 代理生成器，桥接远程客户端和本地引擎
   - `RemoteWorkflowSource`: 远程工作流源数据模型

2. **客户端 SDK**:
   - `workflow_client.py`: 完整的 WorkflowClient SDK
   - `WorkflowSession`: 提供与 WorkflowContext 相同的接口
   - 支持所有工作流步骤类型 (EXECUTE, CONDITION, GENERATE, USER_INPUT, SWITCH, HALT)

3. **集成支持**:
   - 修改 `libraries/workflow_loader.py` 支持加载远程工作流标记文件
   - 修改 `libraries/workflow_state.py` 支持远程工作流源
   - 修改 `libraries/workflow.py` 支持异步工作流加载
   - 在 `api.py` 中添加 WebSocket 端点 `/ws/v1/remote_workflow_connector`

4. **示例和工具**:
   - `example_remote_workflow.py`: 完整的示例远程工作流
   - `test_remote_workflow.py`: 功能测试脚本
   - `quick_start_remote_workflow.py`: 快速启动演示
   - `install_remote_workflow_deps.py`: 依赖安装脚本
   - `workflows/my_remote_flow.remote.yaml`: 示例标记文件

5. **文档**:
   - 完整的 API 参考文档
   - 使用示例和最佳实践
   - 故障排除指南

## 快速开始

1. **安装依赖**:
   ```bash
   python install_remote_workflow_deps.py
   ```

2. **启动服务器**:
   ```bash
   python api.py
   ```

3. **运行快速演示**:
   ```bash
   python quick_start_remote_workflow.py
   ```

4. **运行完整示例**:
   ```bash
   python example_remote_workflow.py
   ```

## 核心优势

1. **完全兼容**: WorkflowClient 提供与 WorkflowContext 完全相同的 API
2. **透明集成**: 对现有工作流引擎完全透明，无需修改核心逻辑
3. **实时通信**: 基于 WebSocket 的实时双向通信
4. **动态加载**: 远程工作流可以像本地工作流一样被发现和管理
5. **错误处理**: 完善的错误处理和资源清理机制

远程工作流功能现在已经完全实现并可以使用！🎉
