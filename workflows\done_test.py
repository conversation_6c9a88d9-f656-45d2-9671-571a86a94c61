from typing import Dict, Any, AsyncGenerator, TYPE_CHECKING
from libraries.workflow_context import WorkflowContext
from libraries.workflow_models import UserInputStep, WorkflowStep, NopStep, ExecuteStep, ConditionStep, ActionDefinition, GenerateStep, CaseDefinition
from message import UserMessage

# --- 工作流元数据 ---
WORKFLOW_DESCRIPTION = "测试工作流"
INITIAL_REGISTERS: Dict[str, Any] = {}


# --- 工作流生成器函数 ---
async def steps(ctx: 'WorkflowContext'):

    await ctx.user_input(
        name="user_input",
        description="等待用户输入消息"
    )

    await ctx.generate(
        name="done_test",
        description="测试工作流，回复'你好'即可"
    )

    await ctx.halt()

    