"""
远程工作流服务器模块。

该模块实现了远程工作流的服务器端支持，允许外部客户端通过 WebSocket 连接
远程定义和驱动 MeowAgent 内部的工作流步骤。

主要组件：
1. RemoteWorkflowSource: 远程工作流源的 Pydantic 模型
2. RemoteWorkflowConnectionManager: 管理 WebSocket 连接和消息路由
3. create_proxy_generator: 创建代理生成器，桥接远程客户端和本地工作流引擎

核心设计理念：
- 对 WorkflowLibrary 和 WorkflowExecutor 透明，它们无需感知工作流是本地还是远程
- 所有远程通信复杂性都封装在此模块中
- 代理生成器实现标准的 AsyncGenerator[WorkflowStep, Any] 接口
"""

import asyncio
import json
import uuid
import traceback
from typing import Dict, Any, Optional, Callable, AsyncGenerator, Set
from datetime import datetime
from pydantic import BaseModel, Field

from log import logger
from libraries.workflow_models import WorkflowStep, PythonWorkflowSource
from libraries.workflow_models import (
    ExecuteStep, ConditionStep, GenerateStep, UserInputStep, 
    HaltStep, SwitchStep, ActionDefinition
)


class RemoteWorkflowSource(BaseModel):
    """
    远程工作流源的 Pydantic 模型，类似于 PythonWorkflowSource。
    
    存储远程工作流的元数据和生成器工厂函数。
    """
    remote_workflow_script_name: str = Field(..., description="远程工作流脚本名称")
    description: str = Field(default="远程工作流", description="工作流描述")
    initial_registers: Dict[str, Any] = Field(default_factory=dict, description="初始寄存器值")
    generator_function_factory: Callable[[str], Callable[[], AsyncGenerator[WorkflowStep, Any]]] = Field(
        ..., 
        description="生成器函数工厂，接收 workflow_instance_id 并返回生成器创建函数",
        exclude=True  # 不序列化函数
    )

    class Config:
        arbitrary_types_allowed = True


class WebSocketMessage(BaseModel):
    """WebSocket 消息的基础模型"""
    type: str
    workflow_instance_id: Optional[str] = None
    client_request_id: Optional[str] = None
    payload: Optional[Dict[str, Any]] = None


class StartWorkflowInstanceMessage(WebSocketMessage):
    """启动工作流实例的消息"""
    type: str = "start_workflow_instance"
    workflow_script_name: str


class DefineStepMessage(WebSocketMessage):
    """定义步骤的消息"""
    type: str = "define_step"
    step_definition: Dict[str, Any]


class StepResultMessage(WebSocketMessage):
    """步骤结果的消息"""
    type: str = "step_result"
    result: Any


class RequestNextStepMessage(WebSocketMessage):
    """请求下一步骤定义的消息"""
    type: str = "request_next_step_definition"


class RemoteWorkflowConnectionManager:
    """
    远程工作流连接管理器，负责管理 WebSocket 连接和消息路由。
    
    这是一个单例类，管理所有远程工作流的连接状态和消息分发。
    """
    
    _instance: Optional['RemoteWorkflowConnectionManager'] = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        # WebSocket 连接映射: workflow_instance_id -> WebSocket
        self.connections: Dict[str, Any] = {}
        
        # 代理生成器映射: workflow_instance_id -> ProxyGenerator
        self.proxy_generators: Dict[str, 'ProxyGenerator'] = {}
        
        # 已注册的远程工作流脚本名称
        self.registered_remote_workflows: Set[str] = set()
        
        # 消息队列: workflow_instance_id -> asyncio.Queue
        self.message_queues: Dict[str, asyncio.Queue] = {}
        
        logger.info("RemoteWorkflowConnectionManager 已初始化")
    
    @classmethod
    async def get_instance(cls) -> 'RemoteWorkflowConnectionManager':
        """获取单例实例"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def register_remote_workflow(self, script_name: str):
        """注册一个远程工作流脚本名称"""
        self.registered_remote_workflows.add(script_name)
        logger.info(f"已注册远程工作流: {script_name}")
    
    def is_remote_workflow_registered(self, script_name: str) -> bool:
        """检查远程工作流是否已注册"""
        return script_name in self.registered_remote_workflows
    
    async def handle_websocket_message(self, websocket, raw_message: str) -> Optional[str]:
        """
        处理来自 WebSocket 的消息。
        
        返回:
            如果是 start_workflow_instance 消息，返回新的 workflow_instance_id
            否则返回 None
        """
        try:
            message_data = json.loads(raw_message)
            message_type = message_data.get("type")
            
            if message_type == "start_workflow_instance":
                return await self._handle_start_workflow_instance(websocket, message_data)
            elif message_type == "define_step":
                await self._handle_define_step(message_data)
            else:
                logger.warning(f"未知的消息类型: {message_type}")
                
        except Exception as e:
            logger.error(f"处理 WebSocket 消息时出错: {e}")
            logger.debug(traceback.format_exc())
        
        return None
    
    async def _handle_start_workflow_instance(self, websocket, message_data: Dict[str, Any]) -> str:
        """处理启动工作流实例的请求"""
        workflow_script_name = message_data.get("workflow_script_name")

        if not workflow_script_name:
            await self._send_error(websocket, "缺少 workflow_script_name")
            raise ValueError("缺少 workflow_script_name")

        # 不再检查是否预先注册，直接创建远程工作流实例
        # 生成新的工作流实例 ID
        workflow_instance_id = f"remote_{uuid.uuid4().hex[:12]}"

        # 存储连接
        self.connections[workflow_instance_id] = websocket

        # 创建消息队列
        self.message_queues[workflow_instance_id] = asyncio.Queue()

        # 动态注册远程工作流（如果尚未注册）
        if not self.is_remote_workflow_registered(workflow_script_name):
            self.register_remote_workflow(workflow_script_name)

        # 发送响应
        response = {
            "type": "workflow_instance_started",
            "workflow_instance_id": workflow_instance_id,
            "workflow_script_name": workflow_script_name
        }
        await websocket.send_text(json.dumps(response))

        logger.info(f"已启动远程工作流实例: {workflow_instance_id} (脚本: {workflow_script_name})")
        return workflow_instance_id
    
    async def _handle_define_step(self, message_data: Dict[str, Any]):
        """处理定义步骤的消息"""
        workflow_instance_id = message_data.get("workflow_instance_id")
        client_request_id = message_data.get("client_request_id")
        step_definition = message_data.get("step_definition")
        
        if not workflow_instance_id or workflow_instance_id not in self.message_queues:
            logger.error(f"无效的 workflow_instance_id: {workflow_instance_id}")
            return
        
        # 将消息放入队列
        queue = self.message_queues[workflow_instance_id]
        await queue.put({
            "type": "define_step",
            "client_request_id": client_request_id,
            "step_definition": step_definition
        })
        
        logger.debug(f"已将步骤定义放入队列: {workflow_instance_id}")
    
    async def send_request_next_step(self, workflow_instance_id: str):
        """向客户端发送请求下一步骤定义的消息"""
        websocket = self.connections.get(workflow_instance_id)
        if not websocket:
            logger.error(f"未找到 workflow_instance_id 的连接: {workflow_instance_id}")
            return
        
        message = {
            "type": "request_next_step_definition",
            "workflow_instance_id": workflow_instance_id
        }
        
        try:
            await websocket.send_text(json.dumps(message))
            logger.debug(f"已发送请求下一步骤定义: {workflow_instance_id}")
        except Exception as e:
            logger.error(f"发送请求下一步骤定义失败: {e}")
    
    async def send_step_result(self, workflow_instance_id: str, client_request_id: str, result: Any):
        """向客户端发送步骤结果"""
        websocket = self.connections.get(workflow_instance_id)
        if not websocket:
            logger.error(f"未找到 workflow_instance_id 的连接: {workflow_instance_id}")
            return
        
        message = {
            "type": "step_result",
            "workflow_instance_id": workflow_instance_id,
            "client_request_id": client_request_id,
            "result": result
        }
        
        try:
            await websocket.send_text(json.dumps(message))
            logger.debug(f"已发送步骤结果: {workflow_instance_id}, request_id: {client_request_id}")
        except Exception as e:
            logger.error(f"发送步骤结果失败: {e}")
    
    async def wait_for_step_definition(self, workflow_instance_id: str) -> Dict[str, Any]:
        """等待客户端发送步骤定义"""
        queue = self.message_queues.get(workflow_instance_id)
        if not queue:
            raise ValueError(f"未找到 workflow_instance_id 的消息队列: {workflow_instance_id}")
        
        # 等待消息
        message = await queue.get()
        return message
    
    async def _send_error(self, websocket, error_message: str):
        """向客户端发送错误消息"""
        try:
            error_response = {
                "type": "error",
                "message": error_message
            }
            await websocket.send_text(json.dumps(error_response))
        except Exception as e:
            logger.error(f"发送错误消息失败: {e}")
    
    def cleanup_connection(self, workflow_instance_id: str):
        """清理连接相关资源"""
        if workflow_instance_id in self.connections:
            del self.connections[workflow_instance_id]
        
        if workflow_instance_id in self.message_queues:
            del self.message_queues[workflow_instance_id]
        
        if workflow_instance_id in self.proxy_generators:
            del self.proxy_generators[workflow_instance_id]
        
        logger.info(f"已清理连接资源: {workflow_instance_id}")


class ProxyGenerator:
    """
    代理生成器，实现 AsyncGenerator[WorkflowStep, Any] 接口。

    桥接远程客户端和本地工作流引擎，将客户端定义的步骤转换为 WorkflowStep 对象。
    """

    def __init__(self, workflow_instance_id: str, script_name: str, conn_manager: RemoteWorkflowConnectionManager):
        self.workflow_instance_id = workflow_instance_id
        self.script_name = script_name
        self.conn_manager = conn_manager
        self.current_client_request_id: Optional[str] = None
        self._is_closed = False

        # 注册到连接管理器
        self.conn_manager.proxy_generators[workflow_instance_id] = self

        logger.info(f"ProxyGenerator 已创建: {workflow_instance_id} (脚本: {script_name})")

    def __aiter__(self):
        """异步迭代器协议"""
        return self

    async def __anext__(self) -> WorkflowStep:
        """获取下一个步骤"""
        if self._is_closed:
            raise StopAsyncIteration("代理生成器已关闭")

        try:
            # 向客户端请求下一步骤定义
            await self.conn_manager.send_request_next_step(self.workflow_instance_id)

            # 等待客户端发送步骤定义
            message = await self.conn_manager.wait_for_step_definition(self.workflow_instance_id)

            # 保存客户端请求 ID
            self.current_client_request_id = message.get("client_request_id")
            step_definition = message.get("step_definition")

            if not step_definition:
                raise ValueError("步骤定义为空")

            # 将步骤定义转换为 WorkflowStep 对象
            workflow_step = self._convert_step_definition_to_workflow_step(step_definition)

            logger.debug(f"ProxyGenerator 生成步骤: {workflow_step.name} (类型: {type(workflow_step).__name__})")
            return workflow_step

        except Exception as e:
            logger.error(f"ProxyGenerator.__anext__ 出错: {e}")
            logger.debug(traceback.format_exc())
            self._is_closed = True
            raise StopAsyncIteration(f"代理生成器出错: {e}")

    async def asend(self, value: Any) -> WorkflowStep:
        """发送值给生成器并获取下一步骤"""
        if self._is_closed:
            raise StopAsyncIteration("代理生成器已关闭")

        try:
            # 将结果发送给客户端
            if self.current_client_request_id:
                await self.conn_manager.send_step_result(
                    self.workflow_instance_id,
                    self.current_client_request_id,
                    value
                )

            # 然后获取下一步骤（类似 __anext__）
            return await self.__anext__()

        except Exception as e:
            logger.error(f"ProxyGenerator.asend 出错: {e}")
            logger.debug(traceback.format_exc())
            self._is_closed = True
            raise StopAsyncIteration(f"代理生成器出错: {e}")

    async def athrow(self, type, value=None, tb=None):
        """向生成器发送异常"""
        logger.warning(f"ProxyGenerator.athrow 被调用: {type}, {value}")

        # 可以选择将错误信息发送给客户端
        if self.current_client_request_id:
            error_info = {
                "error_type": str(type),
                "error_message": str(value) if value else None
            }
            await self.conn_manager.send_step_result(
                self.workflow_instance_id,
                self.current_client_request_id,
                {"error": error_info}
            )

        self._is_closed = True
        raise StopAsyncIteration(f"代理生成器异常: {value}")

    def _convert_step_definition_to_workflow_step(self, step_definition: Dict[str, Any]) -> WorkflowStep:
        """将客户端发送的步骤定义转换为 WorkflowStep 对象"""
        operation = step_definition.get("operation", "").upper()
        name = step_definition.get("name")
        description = step_definition.get("description", "")

        if operation == "EXECUTE":
            # 处理 EXECUTE 步骤
            actions_data = step_definition.get("actions", [])
            actions = []
            for action_data in actions_data:
                action = ActionDefinition(
                    names=action_data.get("names", []),
                    min_calls=action_data.get("min_calls", 1),
                    max_calls=action_data.get("max_calls", 1)
                )
                actions.append(action)

            return ExecuteStep(
                name=name,
                description=description,
                actions=actions
            )

        elif operation == "CONDITION":
            return ConditionStep(
                name=name,
                condition_description=description
            )

        elif operation == "GENERATE":
            wait_user = step_definition.get("wait_user", False)
            return GenerateStep(
                name=name,
                content_description=description,
                wait_user=wait_user
            )

        elif operation == "USER_INPUT":
            return UserInputStep(
                name=name,
                description=description
            )

        elif operation == "HALT":
            return HaltStep(
                name=name,
                description=description
            )

        elif operation == "SWITCH":
            # 处理 SWITCH 步骤
            from libraries.workflow_models import CaseDefinition
            cases_data = step_definition.get("cases", [])
            cases = []
            for case_data in cases_data:
                case = CaseDefinition(
                    name=case_data.get("name"),
                    description=case_data.get("description", "")
                )
                cases.append(case)

            return SwitchStep(
                name=name,
                description=description,
                cases=cases
            )

        else:
            # 默认情况，创建一个基础的 WorkflowStep
            # 注意：这可能需要根据实际的 WorkflowStep 基类来调整
            logger.warning(f"未知的操作类型: {operation}，创建基础 WorkflowStep")
            return WorkflowStep(
                name=name,
                description=description
            )


def create_proxy_generator(
    workflow_instance_id: str,
    script_name: str,
    conn_manager: RemoteWorkflowConnectionManager
) -> AsyncGenerator[WorkflowStep, Any]:
    """
    创建代理生成器的工厂函数。

    参数:
        workflow_instance_id: 工作流实例 ID
        script_name: 远程工作流脚本名称
        conn_manager: 连接管理器实例

    返回:
        代理生成器实例
    """
    return ProxyGenerator(workflow_instance_id, script_name, conn_manager)

