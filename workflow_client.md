# 原始需求

完事具备了！
现在WorkflowContext，其实完全可以实现成远程的！你懂我的意思么。
就是实现一个sdk，里面有WorkflowClient，它可以和本项目的WorkflowServer连接，最终功能和WorkflowContext完全等价，WorkflowClient用起来和 @unified_workflow.py 里面一模一样。你明白我的意思么。

# 详尽方案

**I. 最终目标**

1.  **远程定义与驱动**: 允许外部Python脚本（使用`WorkflowClient` SDK）像编写本地 `WorkflowContext` 脚本一样，远程定义和驱动MeowAgent内部的工作流步骤。
2.  **无缝集成**: `WorkflowLibrary` 和 `WorkflowExecutor` 无需感知当前执行的工作流是由本地Python文件还是远程客户端提供。它们仅与标准的 `WorkflowState` 和 `WorkflowStep` 对象交互。
3.  **抽象封装**: 所有远程通信的复杂性、WebSocket管理、消息协议转换等都被封装在新的 `libraries/workflow_server.py` 模块和 `WorkflowClient` SDK中。
4.  **动态加载**: 远程可驱动的工作流“脚本名”或“端点名”可以像本地工作流一样被 `WorkflowLibrary` 发现和管理。

**II. 核心组件与详细设计**

1.  **`WorkflowClient` (SDK - 外部Python包)**
    *   **接口**: 提供与 `WorkflowContext` 完全相同的异步方法 (`execute`, `condition`, `generate`, `user_input`, `switch`, `halt`)。
    *   **初始化与连接**:
        *   `client = WorkflowClient()`
        *   `await client.connect(meow_agent_url)`: 连接到MeowAgent的专用WebSocket端点。
        *   `session = await client.start_workflow_instance(workflow_script_name="my_remote_flow")`:
            *   向服务器发送一个消息，表明它想为一个名为 "my\_remote\_flow" 的远程工作流启动一个新的执行实例。
            *   服务器会为此创建一个唯一的 `workflow_instance_id`（或 `session_id`）并返回给客户端。客户端后续所有通信都携带此ID。
    *   **方法调用 -> WebSocket消息**:
        *   当 `await session.condition("Is it sunny?")` 被调用:
            1.  客户端为这个请求生成一个唯一的 `client_request_id`。
            2.  通过WebSocket发送消息:
                ```json
                {
                  "type": "DEFINE_STEP",
                  "workflow_instance_id": "server_assigned_instance_id",
                  "client_request_id": "client_req_123",
                  "step_definition": {
                    "operation": "CONDITION",
                    "description": "Is it sunny?",
                    "name": "optional_step_name"
                  }
                }
                ```
            3.  `session.condition(...)` 方法内部 `await` 等待服务器通过WebSocket返回与 `client_request_id: "client_req_123"` 对应的结果。
    *   **接收结果**:
        *   客户端的WebSocket监听器接收来自服务器的 `STEP_RESULT` 消息:
            ```json
            {
              "type": "STEP_RESULT",
              "workflow_instance_id": "server_assigned_instance_id",
              "client_request_id": "client_req_123",
              "result": true // 或用户消息对象，或分支名称等
            }
            ```
        *   根据 `client_request_id` 将结果分派给等待的 `await` 调用。
    *   **服务器请求步骤定义**:
        *   客户端还需要处理服务器主动发来的 `REQUEST_NEXT_STEP_DEFINITION` 消息。这通常发生在服务器端的代理生成器准备好接收下一个步骤定义时。客户端收到此消息后，才知道它可以安全地发送下一个 `DEFINE_STEP` 请求。

2.  **`libraries/workflow_server.py` (MeowAgent内部新增模块)**
    *   **`RemoteWorkflowSource` (Pydantic模型，类似`PythonWorkflowSource`)**:
        *   存储元数据，例如 `remote_workflow_script_name`。
        *   关键属性：`generator_function_factory: Callable[[str], Callable[[], AsyncGenerator[WorkflowStep, Any]]]`。这个工厂函数接收 `workflow_instance_id`，并返回一个真正的“代理生成器”的创建函数（即一个无参数的 `Callable` 返回 `AsyncGenerator`）。
    *   **`RemoteWorkflowConnectionManager` (单例或由`AgentManager`管理的实例)**:
        *   **WebSocket端点逻辑**: (虽然实际端点在 `api.py`，但管理逻辑在此)
            *   处理来自 `WorkflowClient` 的新WebSocket连接。
            *   管理 `workflow_instance_id` 到WebSocket连接的映射。
            *   管理 `workflow_instance_id` 到其对应代理生成器内部通信队列/事件的映射。
        *   **消息路由**:
            *   当收到客户端的 `start_workflow_instance` 请求：
                1.  检查 `workflow_script_name` 是否是已知的可远程驱动的工作流。
                2.  生成 `workflow_instance_id`。
                3.  通知 `AgentManager` (或 `Agent` 实例) 使用此 `workflow_instance_id` 启动一个以此 `workflow_script_name` 为源的工作流（这将导致 `WorkflowState` 创建并开始驱动对应的代理生成器）。
                4.  将 `workflow_instance_id` 与客户端WebSocket关联。
                5.  返回 `workflow_instance_id` 给客户端。
            *   当收到客户端的 `DEFINE_STEP` 消息：将 `step_definition` 和 `client_request_id` 传递给与 `workflow_instance_id` 关联的、正在等待的代理生成器。
            *   当代理生成器产生一个结果需要发回给客户端时：从代理生成器获取结果和 `client_request_id`，构造 `STEP_RESULT` 消息发送给对应的客户端WebSocket。
    *   **`create_proxy_generator(workflow_instance_id: str, script_name: str, conn_manager: RemoteWorkflowConnectionManager) -> AsyncGenerator[WorkflowStep, Any]`**:
        *   这是被 `RemoteWorkflowSource.generator_function_factory` 最终调用的函数，它返回一个实际的异步生成器实例。
        *   **内部状态**: 每个代理生成器实例需要维护:
            *   `current_client_request_id`: 当前由客户端定义的步骤所对应的 `client_request_id`。
        *   **`async def __anext__()`**:
            1.  通过 `conn_manager` 向关联的 `WorkflowClient` 发送 `REQUEST_NEXT_STEP_DEFINITION` 消息（携带 `workflow_instance_id`）。
            2.  `await` `conn_manager` 从客户端接收一个 `DEFINE_STEP` 消息 (包含 `step_definition` 和 `client_request_id`)。
            3.  保存 `client_request_id` 到 `self.current_client_request_id`。
            4.  将 `step_definition` 解析为一个 `WorkflowStep` 对象。
            5.  `yield` 这个 `WorkflowStep` 对象。
        *   **`async def asend(value_from_agent: Any)`**:
            1.  通过 `conn_manager` 将 `value_from_agent` 和 `self.current_client_request_id` 封装成 `STEP_RESULT` 消息，发送给关联的 `WorkflowClient`。
            2.  然后，行为同 `__anext__()`：准备接收下一个步骤定义。
        *   **`async def athrow(type, value=None, traceback=None)` (可选，用于错误处理)**: 如果服务器端 `WorkflowExecutor` 在处理步骤时发生错误并试图通过 `athrow` 传递给生成器，代理生成器可以将此错误信息发送回客户端。
        *   **生命周期**: 当客户端发送一个指示工作流结束的步骤（如 `HaltStep`被客户端定义），或者客户端主动断开连接并通知服务器，或者发生不可恢复错误时，代理生成器应优雅地 `return` (这将引发 `StopAsyncIteration`，被 `WorkflowState` 正常处理)。

3.  **`workflow_loader.py` (适配)**
    *   **识别远程工作流**:
        *   可以定义一种新的文件类型或标记，例如在 `workflows/` 目录下放置 `my_remote_flow.remote.yaml` (或 `.remote_wf.py` 这样的空标记文件)。
        *   当 `load_workflows_from_folder`遇到这种文件：
            1.  它解析出 `workflow_script_name` (例如 "my\_remote\_flow")。
            2.  创建一个 `WorkflowSource` 对象，其 `type` 为 `'python'` (或者一个新的 `'remote_python'` 以示区分，但为了减少对 `WorkflowState` 的改动，保持 `'python'` 可能更好)。
            3.  `source` 字段是一个 `RemoteWorkflowSource` 实例（代替 `PythonWorkflowSource`）。
            4.  `RemoteWorkflowSource.remote_workflow_script_name` 设置为 "my\_remote\_flow"。
            5.  `RemoteWorkflowSource.generator_function_factory` 设置为一个lambda或函数，它知道如何从 `libraries.workflow_server` 模块获取或创建 `create_proxy_generator` 的部分应用版本，该版本已绑定了 `script_name` 和对 `RemoteWorkflowConnectionManager` 的引用。
                例如: `lambda instance_id: workflow_server_module.create_proxy_generator(instance_id, "my_remote_flow", conn_manager_instance)`。

4.  **`api.py` (新增WebSocket端点)**
    *   添加新的WebSocket端点，例如 `@app.websocket("/ws/v1/remote_workflow_connector")`。
    *   此端点的处理器主要职责是将WebSocket连接的生命周期和消息流委托给 `RemoteWorkflowConnectionManager`。
        *   `on_connect`: 将新的WebSocket连接注册到 `conn_manager`。
        *   `on_receive (text/bytes)`: 将收到的消息和WebSocket对象本身传递给 `conn_manager` 进行解析和路由。
        *   `on_disconnect`: 通知 `conn_manager` 此连接已断开，以便清理相关会话和代理生成器。

5.  **`WorkflowState.py` (理论上无需或只需微小改动)**
    *   当 `set_python_workflow` 被调用时，如果传入的 `PythonWorkflowSource` (或适配后的 `RemoteWorkflowSource`) 的 `generator_function` 是代理生成器的创建函数，`WorkflowState` 会正常调用它得到代理生成器实例。
    *   后续对 `generator_instance.__anext__()` 和 `generator_instance.asend()` 的调用将直接与代理生成器交互。

6.  **`WorkflowLibrary.py` 和 `WorkflowExecutor.py` (理论上无需改动)**
    *   它们依赖于 `WorkflowState` 提供的 `current_step` 和推进方法。由于代理生成器 `yield` 的是标准的 `WorkflowStep` 对象，并正确响应 `asend`，这两个核心组件的行为保持不变。

**III. 详细交互流程 (Condition步骤，结合新设计):**

1.  **Agent启动与工作流加载**:
    *   MeowAgent启动，`workflow_loader.py` 扫描到 `my_remote_flow.remote.yaml`。
    *   它创建一个 `WorkflowSource(type='python', source=RemoteWorkflowSource(remote_workflow_script_name="my_remote_flow", generator_function_factory=...))` 并加载到 `WorkflowLibrary`。

2.  **客户端连接与启动实例**:
    *   `WorkflowClient` SDK: `await client.connect(...)`, `session = await client.start_workflow_instance(workflow_script_name="my_remote_flow")`.
    *   客户端向 `/ws/v1/remote_workflow_connector` 发送 `start_workflow_instance` 消息。
    *   `api.py` 端点将消息和连接交给 `RemoteWorkflowConnectionManager`。
    *   `RemoteWorkflowConnectionManager`:
        *   验证 "my\_remote\_flow" 是一个已知的远程脚本名。
        *   生成 `workflow_instance_id_123`。
        *   **关键**: 它现在需要一种方式让一个 `Agent` 实例被创建（如果还没有与此客户端关联的）并让该 `Agent` 的 `WorkflowLibrary` 调用 `set_current_workflow("my_remote_flow", workflow_instance_id="workflow_instance_id_123")`。
            *   `WorkflowLibrary.set_current_workflow` 内部，当源是 `RemoteWorkflowSource` 时，它会使用 `generator_function_factory("workflow_instance_id_123")` 来获取一个配置了此实例ID的代理生成器创建函数，然后调用该函数得到代理生成器实例。
            *   `WorkflowState` 开始驱动这个代理生成器。
        *   `RemoteWorkflowConnectionManager` 将 `workflow_instance_id_123` 与客户端WebSocket和新创建的代理生成器的通信渠道关联起来。
        *   向客户端返回 `workflow_instance_id_123`。

3.  **代理生成器首次 `__anext__()`**:
    *   `WorkflowState` 调用代理生成器（实例ID为 `workflow_instance_id_123`）的 `__anext__()`。
    *   代理生成器通过 `RemoteWorkflowConnectionManager` 向客户端 `workflow_instance_id_123` 发送 `REQUEST_NEXT_STEP_DEFINITION`。
    *   代理生成器 `await` 等待客户端的 `DEFINE_STEP` 响应。

4.  **客户端定义Condition步骤**:
    *   客户端收到 `REQUEST_NEXT_STEP_DEFINITION`。
    *   用户脚本调用 `await session.condition("Is it sunny?")`。
    *   客户端发送 `DEFINE_STEP` 消息（包含`operation:CONDITION`, `desc:"Is it sunny?"`, `client_request_id_abc`）给服务器（针对 `workflow_instance_id_123`）。

5.  **代理生成器接收并Yield步骤**:
    *   `RemoteWorkflowConnectionManager` 将 `DEFINE_STEP` 消息路由给等待的代理生成器 `workflow_instance_id_123`。
    *   代理生成器的 `__anext__()` 调用解除阻塞，它保存 `client_request_id_abc`，创建 `ConditionStep` 对象，并 `yield` 它。

6.  **核心引擎处理**:
    *   `WorkflowState` 得到 `ConditionStep`。
    *   `WorkflowExecutor` 处理它，LLM被调用，`WorkflowLibrary.condition_branch(True)` 被触发。

7.  **结果回传**:
    *   `WorkflowLibrary.condition_branch` 调用 `state.advance_python_step_send(True)`.
    *   `WorkflowState` 调用代理生成器 `workflow_instance_id_123` 的 `asend(True)`.

8.  **代理生成器发送结果给客户端**:
    *   代理生成器的 `asend(True)` 方法被调用。
    *   它通过 `RemoteWorkflowConnectionManager` 将结果 `True` 和之前保存的 `client_request_id_abc` 封装成 `STEP_RESULT` 消息，发送给客户端 `workflow_instance_id_123`。
    *   `asend()` 完成后，其行为类似于 `__anext__()`，再次向客户端请求下一个步骤定义。

9.  **客户端接收结果**:
    *   `WorkflowClient` 的WebSocket监听器收到 `STEP_RESULT`，匹配 `client_request_id_abc`。
    *   `session.condition(...)` 调用解除阻塞，返回 `True`。

**IV. 关键优势和考虑**

*   **高度抽象**: `WorkflowLibrary` 和 `WorkflowExecutor` 保持纯净，只与 `WorkflowState` 和 `WorkflowStep` 交互。
*   **封装性**: 所有远程通信的复杂性都在 `workflow_server.py` 和 `WorkflowClient` SDK 中。
*   **可测试性**: 代理生成器可以独立测试其与 `RemoteWorkflowConnectionManager` 的交互。核心引擎也可以用模拟的生成器测试。
*   **`RemoteWorkflowConnectionManager` 的复杂性**: 这个组件变得非常核心，需要健壮地处理并发连接、会话状态、消息路由和与代理生成器的同步。可能需要为每个活动的代理生成器实例使用`asyncio.Queue`来传递来自客户端的步骤定义，以及传递回客户端的结果。
*   **启动流程**: 如何在客户端请求 `start_workflow_instance` 时，优雅地触发服务器端对应 `Agent` 的 `WorkflowLibrary` 去设置并启动这个特定的远程工作流实例，这需要仔细设计。`AgentManager` 可能需要扮演协调角色，或者 `RemoteWorkflowConnectionManager` 需要权限来请求 `AgentManager` 执行此操作。

这个方案在架构上更为清晰和健壮，因为它将远程驱动的特性巧妙地伪装成了MeowAgent已有的Python生成器工作流模式。挑战主要在于 `RemoteWorkflowConnectionManager` 和代理生成器 (`create_proxy_generator`) 的健壮实现。
