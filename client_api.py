@app.websocket("/ws/v1/remote_workflow_connector")
async def remote_workflow_websocket_endpoint(websocket: WebSocket):
    """远程工作流连接器的 WebSocket 端点"""
    await websocket.accept()

    # 获取连接管理器
    from libraries.workflow_server import RemoteWorkflowConnectionManager
    conn_manager = await RemoteWorkflowConnectionManager.get_instance()

    current_workflow_instance_ids = []

    try:
        logger.info("远程工作流客户端已连接")

        while True:
            # 接收消息
            raw_message = await websocket.receive_text()
            logger.debug(f"收到远程工作流消息: {raw_message}")

            # 处理消息
            workflow_instance_id = await conn_manager.handle_websocket_message(websocket, raw_message)

            # 如果是启动工作流实例的消息，记录实例 ID 用于清理
            if workflow_instance_id:
                current_workflow_instance_ids.append(workflow_instance_id)
                try:
                    # 动态创建远程工作流并启动 Agent
                    await start_remote_workflow_agent(workflow_instance_id, conn_manager)
                    logger.info(f"远程工作流实例已启动: {workflow_instance_id}")
                except Exception as e:
                    logger.error(f"启动远程工作流实例失败: {e}")
                    logger.debug(traceback.format_exc())

    except Exception as e:
        logger.error(f"远程工作流 WebSocket 连接出错: {e}")
        logger.debug(traceback.format_exc())
    finally:
        # 清理所有相关的工作流实例
        for instance_id in current_workflow_instance_ids:
            try:
                conn_manager.cleanup_connection(instance_id)
                logger.info(f"已清理远程工作流实例: {instance_id}")
            except Exception as e:
                logger.error(f"清理远程工作流实例失败 {instance_id}: {e}")

        logger.info("远程工作流客户端连接已关闭")


async def start_remote_workflow_agent(workflow_instance_id: str, conn_manager):
    """
    为远程工作流实例启动 Agent

    参数:
        workflow_instance_id: 远程工作流实例 ID
        conn_manager: 远程工作流连接管理器
    """
    try:
        # 动态创建远程工作流源
        from libraries.workflow_server import RemoteWorkflowSource, create_proxy_generator
        from libraries.workflow_models import WorkflowSource

        # 提取脚本名称（从 workflow_instance_id 中获取或使用默认值）
        script_name = f"remote_workflow_{workflow_instance_id}"

        # 创建生成器工厂函数
        def generator_factory(instance_id: str):
            def wrapper():
                return create_proxy_generator(instance_id, script_name, conn_manager)
            return wrapper

        # 创建 RemoteWorkflowSource
        remote_source = RemoteWorkflowSource(
            remote_workflow_script_name=script_name,
            description=f"动态远程工作流: {script_name}",
            initial_registers={},
            generator_function_factory=generator_factory
        )

        # 创建 WorkflowSource 对象
        workflow_source = WorkflowSource(
            type='python',
            source=remote_source
        )

        # 获取 AgentManager 实例
        agent_manager = await AgentManager.get_instance()

        # 动态添加到工作流库
        workflow_library = agent_manager.workflow_library
        workflow_library.workflows[script_name] = workflow_source

        # 创建新的 Agent 实例来运行远程工作流
        from agent import Agent

        # 使用默认配置创建 Agent
        agent = Agent(
            name=f"RemoteAgent_{workflow_instance_id}",
            description=f"远程工作流代理: {workflow_instance_id}",
            workflow_library=workflow_library
        )

        # 启动远程工作流
        await agent.start_workflow(script_name)

        logger.info(f"远程工作流 Agent 已启动: {workflow_instance_id} -> {script_name}")

    except Exception as e:
        logger.error(f"启动远程工作流 Agent 失败: {e}")
        logger.debug(traceback.format_exc())
        raise

