"""
工作流加载器模块。

该模块负责从指定的文件夹中发现、解析和加载工作流定义。
支持三种格式的工作流定义：
1.  **YAML 文件 (.yaml, .yml)**：
    -   工作流的结构和步骤在 YAML 文件中定义。
    -   使用 `WorkflowDefinition` Pydantic 模型进行数据验证。
    -   每个 YAML 文件代表一个独立的工作流。
2.  **传统 Python 模块 (.py)**：
    -   工作流的逻辑通过 Python 异步生成器函数实现。
    -   每个 Python 文件应包含一个名为 `steps` 的异步生成器函数，该函数直接 yield WorkflowStep 对象。
3.  **新式 Python 模块 (.py with WorkflowContext)**：
    -   工作流的逻辑通过 Python async 函数实现，使用 WorkflowContext 进行步骤定义。
    -   每个 Python 文件应包含一个名为 `steps` 的异步函数，接受一个 WorkflowContext 参数。
    -   模块可以包含可选的元数据：
        -   `WORKFLOW_DESCRIPTION` (str): 工作流的描述。
        -   `INITIAL_REGISTERS` (dict): 工作流的初始寄存器值。

主要功能通过 `load_workflows_from_folder` 函数实现，该函数：
-   扫描指定目录下的所有文件。
-   根据文件扩展名区分 YAML 和 Python 工作流。
-   动态导入 Python 模块或解析 YAML 文件。
-   对于 Python 工作流，自动检测是传统生成器风格还是新式 WorkflowContext 风格，并进行相应包装。
-   对加载的工作流数据进行结构验证。
-   将成功加载的工作流组织成一个字典，其中键是工作流名称（通常是文件名，不含扩展名），
    值是 `WorkflowSource` 对象。`WorkflowSource` 包含工作流的类型（'yaml' 或 'python'）
    以及其实际定义（`WorkflowDefinition` 或 `PythonWorkflowSource`）。
-   记录详细的加载信息，包括成功加载的工作流数量、跳过的文件以及加载过程中发生的任何错误。

此模块旨在提供一种灵活的方式来管理和扩展 MeowAgent 中的工作流，
允许开发者使用声明式的 YAML 或编程式的 Python 来定义复杂的任务序列。
"""
from typing import Dict, Any, Union, Callable, AsyncGenerator
from log import logger
import os
import yaml
import traceback
import importlib
import importlib.util
import inspect # 用于检查是否为异步生成器函数
from libraries.workflow_models import WorkflowDefinition, WorkflowSource, PythonWorkflowSource, WorkflowStep


async def _create_context_workflow_generator(
    context_workflow_func: Callable,
    registers: Dict[str, Any]
) -> AsyncGenerator[WorkflowStep, Any]:
    """
    创建一个包装器异步生成器，用于将 WorkflowContext 风格的工作流函数
    适配到标准的 AsyncGenerator[WorkflowStep, Any] 接口。
    
    参数:
        context_workflow_func: 使用 WorkflowContext 的工作流函数
        registers: 工作流寄存器的引用
        
    Yields:
        WorkflowStep: 工作流步骤对象
    """
    from libraries.workflow_context import WorkflowContext, StepCommunicator
    from libraries.workflow_models import HaltStep
    import asyncio
    
    # 创建通信器和上下文
    communicator = StepCommunicator()
    context = WorkflowContext(communicator, registers)
    
    # 启动用户的工作流函数
    user_workflow_task = asyncio.create_task(context_workflow_func(context))
    next_step_task = None
    sent_value = None
    
    try:
        while not user_workflow_task.done():
            # 创建获取下一步骤的任务
            next_step_task = asyncio.create_task(communicator.get_next_step())
            
            # 并发等待：用户工作流任务完成 或 下一步骤可用
            done, pending = await asyncio.wait(
                [user_workflow_task, next_step_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 检查哪个任务先完成
            if user_workflow_task in done:
                # 用户工作流任务已完成(正常或异常)
                if next_step_task and not next_step_task.done():
                    next_step_task.cancel()
                    try:
                        await next_step_task
                    except asyncio.CancelledError:
                        pass
                
                # 检查用户工作流是否有异常
                if user_workflow_task.exception():
                    logger.error(f"用户工作流函数抛出异常: {user_workflow_task.exception()}")
                    raise user_workflow_task.exception()
                else:
                    logger.info("用户工作流函数正常结束")
                    return  # 工作流正常结束，结束生成器
            
            # 下一步骤任务完成
            if next_step_task in done:
                step = next_step_task.result()
                
                # 检查是否为 HaltStep
                is_halt_step = isinstance(step, HaltStep)

                # 如果是 HaltStep，在发送结果后直接结束生成器
                if is_halt_step:
                    logger.info("WorkflowContext 工作流遇到 HaltStep，正常结束生成器")
                    # 取消用户工作流任务并正常结束生成器
                    if not user_workflow_task.done():
                        user_workflow_task.cancel()
                        try:
                            await user_workflow_task
                        except asyncio.CancelledError:
                            pass
                    # 正常结束生成器
                    return
                
                # yield 步骤给外部 (WorkflowState)
                sent_value = yield step
                
                # 将外部发送的值传回给 WorkflowContext
                communicator.send_result(sent_value)
                
                
            
    except Exception as e:
        # 如果生成器过程中出现异常
        logger.error(f"包装器生成器出现异常: {e}")
        logger.debug(f"异常详情: {traceback.format_exc()}")
        raise
    finally:
        # 清理：确保用户任务和步骤任务被正确终止
        if user_workflow_task and not user_workflow_task.done():
            user_workflow_task.cancel()
            try:
                await user_workflow_task
            except asyncio.CancelledError:
                pass
        
        if next_step_task and not next_step_task.done():
            next_step_task.cancel()
            try:
                await next_step_task
            except asyncio.CancelledError:
                pass


def _detect_python_workflow_type(steps_func: Callable) -> str:
    """
    检测 Python 工作流的类型。
    
    参数:
        steps_func: 工作流的 steps 函数
        
    返回:
        'generator': 传统的异步生成器函数
        'context': 使用 WorkflowContext 的异步函数
        'unknown': 无法识别的类型
    """
    if inspect.isasyncgenfunction(steps_func):
        return 'generator'
    elif inspect.iscoroutinefunction(steps_func):
        # 检查函数签名是否接受参数
        sig = inspect.signature(steps_func)
        params = list(sig.parameters.values())
        
        # 如果有参数且第一个参数可能是 WorkflowContext
        if len(params) > 0:
            # 可以通过参数名或类型注解来进一步判断
            first_param = params[0]
            param_name = first_param.name.lower()
            
            # 常见的上下文参数名
            if param_name in ('ctx', 'context', 'workflow_context'):
                return 'context'
            
            # 检查类型注解
            if first_param.annotation != inspect.Parameter.empty:
                annotation_str = str(first_param.annotation)
                if 'WorkflowContext' in annotation_str:
                    return 'context'
                    
        # 如果是协程函数但无法确定是否使用 context，默认认为是 context 类型
        # 因为传统方式应该使用 async generator
        return 'context'
    
    return 'unknown'


def _create_context_wrapper_function(
    context_workflow_func: Callable,
    registers: Dict[str, Any]
) -> Callable[[], AsyncGenerator[WorkflowStep, Any]]:
    """
    创建一个包装器函数，返回包装后的异步生成器。
    
    参数:
        context_workflow_func: 使用 WorkflowContext 的工作流函数
        registers: 初始寄存器值
        
    返回:
        一个无参数的函数，调用时返回 AsyncGenerator[WorkflowStep, Any]
    """
    def wrapper() -> AsyncGenerator[WorkflowStep, Any]:
        # 创建寄存器副本，每次调用时都是独立的
        registers_copy = registers.copy()
        return _create_context_workflow_generator(context_workflow_func, registers_copy)
    
    return wrapper


async def load_workflows_from_folder(folder_path: str) -> Dict[str, WorkflowSource]:
    """
    从指定文件夹加载所有工作流定义 (YAML 或 Python)。
    
    参数:
        folder_path: 工作流文件夹路径
        
    返回:
        Dict[str, WorkflowSource]: 工作流名称到 WorkflowSource 对象的映射，
                                    包含工作流来源类型和具体定义。
    """

    workflows: Dict[str, WorkflowSource] = {}
    try:
        # 确保文件夹存在
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
            logger.info(f"创建工作流文件夹: {folder_path}")

        if not os.path.isdir(folder_path):
            logger.warning(f"'{folder_path}' 不是一个目录")
            return workflows

        # 加载所有文件
        loaded_count = 0
        python_loaded_count = 0
        yaml_loaded_count = 0
        skipped_files = []

        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            workflow_name = os.path.splitext(filename)[0]

            # 跳过特殊文件和非 .py/.yaml/.yml 文件
            if filename.startswith('__') or filename == 'library.py':
                skipped_files.append(filename)
                continue

            # --- 处理 YAML 文件 --- 
            if filename.endswith(('.yaml', '.yml')):
                try:
                    # 尝试读取和解析YAML文件
                    with open(file_path, 'r', encoding='utf-8') as f:
                        workflow_data = yaml.safe_load(f)

                    # 使用Pydantic模型解析和验证
                    workflow_definition = WorkflowDefinition(**workflow_data)
                    # 创建 WorkflowSource 对象
                    workflows[workflow_name] = WorkflowSource(
                        type='yaml',
                        source=workflow_definition
                    )
                    yaml_loaded_count += 1
                    logger.info(f"成功加载 YAML 工作流: {workflow_name}")
                except Exception as e:
                    logger.error(f"YAML 工作流 '{workflow_name}' 加载或验证失败: {e}")
                    logger.debug(f"失败的工作流数据 ({filename}): {workflow_data if 'workflow_data' in locals() else '无法读取'}")
                    logger.debug(traceback.format_exc())
                    continue # 继续处理下一个文件
                    


            # --- 处理 Python 文件 ---
            elif filename.endswith('.py'):
                try:
                    # 动态导入模块
                    module_name = f"{os.path.basename(folder_path)}.{workflow_name}"
                    spec = importlib.util.spec_from_file_location(module_name, file_path)
                    if spec and spec.loader:
                        module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(module)
                    else:
                        logger.warning(f"无法加载 Python 工作流模块: {filename}")
                        continue

                    # 查找 steps 函数
                    steps_func = getattr(module, 'steps', None)
                    if not steps_func:
                        logger.warning(f"Python 工作流 '{filename}' 中未找到名为 'steps' 的函数，已跳过。")
                        continue

                    # 检测工作流类型
                    workflow_type = _detect_python_workflow_type(steps_func)

                    if workflow_type == 'unknown':
                        logger.warning(f"Python 工作流 '{filename}' 中的 'steps' 函数类型无法识别，已跳过。")
                        continue

                    # 查找元数据
                    description = getattr(module, 'WORKFLOW_DESCRIPTION', f"Python workflow from {filename}")
                    initial_registers = getattr(module, 'INITIAL_REGISTERS', {})
                    if not isinstance(description, str):
                         logger.warning(f"Python 工作流 '{filename}' 的 WORKFLOW_DESCRIPTION 不是字符串，使用默认值。")
                         description = f"来自 {filename} 的 Python 工作流"
                    if not isinstance(initial_registers, dict):
                         logger.warning(f"Python 工作流 '{filename}' 的 INITIAL_REGISTERS 不是字典，使用空字典。")
                         initial_registers = {}

                    # 根据工作流类型创建不同的生成器函数
                    if workflow_type == 'generator':
                        # 传统的异步生成器函数，直接使用
                        generator_function = steps_func
                        logger.info(f"检测到传统异步生成器工作流: {workflow_name}")
                    elif workflow_type == 'context':
                        # 使用 WorkflowContext 的工作流，需要包装
                        generator_function = _create_context_wrapper_function(steps_func, initial_registers)
                        logger.info(f"检测到 WorkflowContext 工作流: {workflow_name}")
                    else:
                        # 不应该到达这里，但保险起见
                        logger.error(f"内部错误：未处理的工作流类型 '{workflow_type}' for {filename}")
                        continue

                    # 创建 PythonWorkflowSource 和 WorkflowSource 对象
                    python_source = PythonWorkflowSource(
                        generator_function=generator_function,
                        description=description,
                        initial_registers=initial_registers
                    )
                    workflows[workflow_name] = WorkflowSource(
                        type='python',
                        source=python_source
                    )
                    python_loaded_count += 1
                    logger.info(f"成功加载 Python 工作流: {workflow_name} (类型: {workflow_type})")

                except Exception as e:
                    logger.error(f"Python 工作流 '{workflow_name}' 加载失败: {e}")
                    logger.debug(traceback.format_exc())
                    continue # 继续处理下一个文件
            else:
                # 其他文件类型，跳过
                skipped_files.append(filename)
                continue

        loaded_count = yaml_loaded_count + python_loaded_count
        logger.info(f"工作流库: 共加载 {loaded_count} 个工作流 ({yaml_loaded_count} YAML, {python_loaded_count} Python)。")
        if skipped_files:
            logger.debug(f"跳过的文件: {skipped_files}")

        return workflows

    except Exception as e:
        logger.error(f"加载工作流时发生意外错误: {e}")
        logger.debug(traceback.format_exc())
        return {}